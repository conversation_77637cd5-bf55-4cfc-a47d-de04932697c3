package services

import (
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/Norray/xrocket/xamqp"
	uuid "github.com/satori/go.uuid"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xtool"
	"github.com/jinzhu/copier"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

const (
	CreateInvoiceDraftTask           = "create_invoice_draft_task" // 生成發票草稿任務
	JobApplicationProgressUpcoming   = "UPCOMING"                  // 待開始
	JobApplicationProgressInProgress = "IN_PROGRESS"               // 進行中
	JobApplicationProgressApplied    = "APPLIED"                   // 已申請
	JobApplicationProgressComplete   = "COMPLETE"                  // 已完成
	JobApplicationProgressCancel     = "CANCEL"                    // 已取消

	GenerateCompensationConfirmationNoteTask = "generate_compensation_confirmation_note_task"
)

// 工作職位申請服務
var JobApplicationService = new(jobApplicationService)

type jobApplicationService struct{}

// 檢查工作職位申請ID是否存在
func (s *jobApplicationService) CheckIdExist(db *gorm.DB, m *model.JobApplication, id uint64, userId ...uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.job_application.id.does_not_exist",
		Other: "No such application record, please try after reloading.",
	}
	var err error
	builder := db.Model(&model.JobApplication{}).Where("id = ?", id)
	if len(userId) > 0 {
		builder = builder.Where("user_id = ?", userId[0])
	}
	if err = builder.First(&m).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if xgorm.IsNotFoundErr(err) {
		return false, msg, nil
	} else {
		return true, msg, nil
	}
}

func (s *jobApplicationService) CheckIdExistByFacility(db *gorm.DB, m *model.JobApplication, id uint64, facilityId uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.job_application.id.does_not_exist",
		Other: "No such application record, please try after reloading.",
	}
	var err error
	builder := db.Model(&model.JobApplication{}).Where("facility_id = ?", facilityId).Where("id = ?", id)
	if err = builder.First(&m).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if xgorm.IsNotFoundErr(err) {
		return false, msg, nil
	} else {
		return true, msg, nil
	}
}

// 檢查機構是否存在專業人士的申請記錄
func (s *jobApplicationService) CheckFacilityProfessionalApplication(db *gorm.DB, facilityId uint64, professionalId uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.job_application.id.does_not_exist",
		Other: "No such application record, please try after reloading.",
	}
	var err error
	if err = db.
		Where("facility_id = ? AND professional_id = ?", facilityId, professionalId).
		Where("status IS NOT NULL AND status <> ?", model.JobApplicationStatusWithdraw).
		First(&model.JobApplication{}).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if xgorm.IsNotFoundErr(err) {
		return false, msg, nil
	} else {
		return true, msg, nil
	}
}

// 檢查機構是否存在專業人士的申請記錄
func (s *jobApplicationService) CheckFacilityProfessionalFile(db *gorm.DB, professionalId, professionalFileId uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.job_application.id.does_not_exist",
		Other: "No such application record, please try after reloading.",
	}
	var err error
	var professionalFileRelation model.ProfessionalFileRelation
	if err = db.
		Where("professional_file_id = ?", professionalFileId).
		Where("professional_id = ?", professionalId).
		First(&professionalFileRelation).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if xgorm.IsNotFoundErr(err) {
		return false, msg, nil
	}
	var professionalFile model.ProfessionalFile
	if err = db.
		Where("id = ?", professionalFileId).
		Where("user_id = ?", professionalFileRelation.UserId).
		First(&professionalFile).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if xgorm.IsNotFoundErr(err) {
		return false, msg, nil
	}
	if canAccess, ok := FacilityAccessProfessionalFileTypeMap[professionalFile.FileCode]; ok && canAccess {
		return true, msg, nil
	} else {
		return false, msg, nil
	}
}

// 檢查機構是否存在專業人士的申請記錄
func (s *jobApplicationService) CheckFacilityProfessionalApplicationByUserId(db *gorm.DB, facilityId uint64, professionalUserId uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.job_application.id.does_not_exist",
		Other: "No such application record, please try after reloading.",
	}
	var err error
	if err = db.
		Where("facility_id = ? AND user_id = ?", facilityId, professionalUserId).
		Where("status IS NOT NULL AND status <> ?", model.JobApplicationStatusWithdraw).
		First(&model.JobApplication{}).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if xgorm.IsNotFoundErr(err) {
		return false, msg, nil
	} else {
		return true, msg, nil
	}
}

// region ---------------------------------------------------- 獲取工作職位申請列表 ----------------------------------------------------

// 工作職位申請列表請求
type JobApplicationListForFacilityReq struct {
	FacilityId uint64 `form:"facilityId" binding:"required"`                                                                                                                                  // 所屬機構Id
	JobId      uint64 `form:"jobId" binding:"required"`                                                                                                                                       // 工作職位Id
	Status     string `form:"status" binding:"omitempty,oneof=APPLY CHATTING WITHDRAW INVITE REJECT DECLINE ACCEPT UPCOMING APPLICATION_CANCEL FACILITY_CANCEL PROFESSIONAL_CANCEL COMPLETE"` // 申請狀態 APPLY: 申請 CHATTING: 聊天 WITHDRAW: 撤銷 INVITE: 邀請 REJECT: 拒絕 DECLINE: 拒絕 ACCEPT: 接受 UPCOMING: 即將開始 APPLICATION_CANCEL FACILITY_CANCEL: 機構取消 PROFESSIONAL_CANCEL: 專業人員取消 COMPLETE: 完成
	Accept     string `form:"accept" binding:"omitempty,oneof=Y N"`                                                                                                                           // 是否已經取錄
	ReqUserId  uint64 `form:"-"`
}

func (req *JobApplicationListForFacilityReq) GetBaseReq() JobApplicationListBaseReq {
	var resp JobApplicationListBaseReq
	_ = copier.Copy(&resp, req)
	return resp
}

type JobApplicationListForSystemReq struct {
	UserId     uint64 `form:"userId"`                                                                                                                                                         // 用戶Id
	FacilityId uint64 `form:"facilityId"`                                                                                                                                                     // 機構Id
	JobId      uint64 `form:"jobId"`                                                                                                                                                          // 工作職位Id
	Status     string `form:"status" binding:"omitempty,oneof=APPLY CHATTING WITHDRAW INVITE REJECT DECLINE ACCEPT UPCOMING APPLICATION_CANCEL FACILITY_CANCEL PROFESSIONAL_CANCEL COMPLETE"` // 申請狀態 APPLY: 申請 CHATTING: 聊天 WITHDRAW: 撤銷 INVITE: 邀請 REJECT: 拒絕 DECLINE: 拒絕 ACCEPT: 接受 UPCOMING: 即將開始 APPLICATION_CANCEL FACILITY_CANCEL: 機構取消 PROFESSIONAL_CANCEL: 專業人員取消 COMPLETE: 完成
	Accept     string `form:"accept" binding:"omitempty,oneof=Y N"`                                                                                                                           // 是否已經取錄
	ReqUserId  uint64 `form:"-"`
}

func (req *JobApplicationListForSystemReq) GetBaseReq() JobApplicationListBaseReq {
	var resp JobApplicationListBaseReq
	_ = copier.Copy(&resp, req)
	return resp
}

type JobApplicationListBaseReq struct {
	UserId     uint64
	FacilityId uint64
	JobId      uint64
	Status     string
	Accept     string
	ReqUserId  uint64
}

// 工作職位申請列表響應
type JobApplicationListResp struct {
	ApplicationId        uint64 `json:"applicationId"`        // 申請Id
	FacilityId           uint64 `json:"facilityId"`           // 機構Id
	JobId                uint64 `json:"jobId"`                // 工作Id
	ProfessionalId       uint64 `json:"professionalId"`       // 專業人員Id
	FirstName            string `json:"firstName"`            // 專業人員名字
	LastName             string `json:"lastName"`             // 專業人員姓氏
	ProfessionalPhotoId  uint64 `json:"professionalPhotoId"`  // 專業人員照片Id
	Score                int    `json:"score"`                // 匹配分數
	Status               string `json:"status"`               // 申請狀態 APPLY: 申請 CHATTING: 聊天 WITHDRAW: 撤銷 INVITE: 邀請 REJECT: 拒絕 DECLINE: 拒絕 ACCEPT: 接受 UPCOMING: 即將開始 APPLICATION_CANCEL FACILITY_CANCEL: 機構取消 PROFESSIONAL_CANCEL: 專業人員取消 COMPLETE: 完成
	Accept               string `json:"accept"`               // 是否已經取錄
	ApplyTime            string `json:"-"`                    // 申請時間（隱藏）
	Language             string `json:"-"`                    // 專業人士語言（內部使用）
	MatchedLanguages     string `json:"matchedLanguages"`     // 符合職位的語言
	MatchedLanguageNames string `json:"matchedLanguageNames"` // 符合職位的語言
}

// 獲取工作職位申請列表 (機構)
func (s *jobApplicationService) List(db *gorm.DB, req JobApplicationListBaseReq, pageSet *xresp.PageSet, sortSet xresp.SortingSet) ([]JobApplicationListResp, error) {
	var resp []JobApplicationListResp
	var err error
	var job model.Job
	if err = db.Where("facility_id = ?", req.FacilityId).Where("id = ?", req.JobId).First(&job).Error; err != nil {
		return resp, err
	}

	var facilityProfile model.FacilityProfile
	if err = db.Where("facility_id = ?", req.FacilityId).
		Where("status = ?", model.FacilityProfileStatusApproved).
		Where("id = ?", job.FacilityProfileId).
		First(&facilityProfile).Error; err != nil {
		return resp, err
	}

	builder := db.Table("job_application AS ja").
		Joins("JOIN job AS j ON ja.job_id = j.id").
		Joins("JOIN professional AS p ON ja.professional_id = p.id").
		Joins("JOIN user AS u ON p.user_id = u.id").
		Joins("JOIN professional_file_relation AS pfr ON pfr.professional_id = p.id ").
		Joins("JOIN professional_file AS pf ON pfr.professional_file_id = pf.id AND p.user_id = u.id AND pf.file_code = ?", model.ProfessionalFileCodePhoto).
		Select([]string{
			"ja.id AS application_id",
			"ja.facility_id",
			"ja.job_id",
			"ja.professional_id",
			"p.first_name",
			"p.last_name",
			"pf.id AS professional_photo_id",
			"ja.score",
			"ja.status",
			"ja.accept",
			"ja.apply_time",
			"p.language",
		}).
		Where("ja.deleted <> ?", model.JobApplicationDeletedY)

	err = FacilityUserDepartmentService.GetCanAccessDepartmentByUserId(db, req.ReqUserId, func(departments []model.Department) {
		builder = builder.Joins("JOIN job_department AS jd ON jd.job_id = j.id")
		builder = builder.Scopes(FacilityUserDepartmentService.FilterByCanAccessDepartment("jd.department_id", departments))
	})
	if err != nil {
		return nil, err
	}
	if req.UserId > 0 {
		builder = builder.Where("ja.user_id = ?", req.UserId)
	}
	if req.JobId > 0 {
		builder = builder.Where("ja.job_id = ?", req.JobId)
	}
	if req.FacilityId > 0 {
		builder = builder.Where("ja.facility_id = ?", req.FacilityId)
	}
	// 如果指定了狀態，則按狀態過濾
	if req.Status != "" {
		builder = builder.Where("ja.status = ?", req.Status)
	}

	if req.Accept != "" {
		if req.Accept == model.JobApplicationAcceptY {
			builder = builder.Where("ja.accept = ?", req.Accept).
				Where("ja.status = ?", model.JobApplicationStatusAccept)
		} else {
			builder = builder.Where("ja.accept = ? OR ja.status != ?", req.Accept, model.JobApplicationStatusAccept)
		}
	}
	if job.ShiftAllocation == model.JobShiftAllocationAutomatic {
		// 排序：1.發佈後兩小時內的排前, 2.發佈後兩小時外的排後
		builder = builder.
			Where("DATE_ADD(j.publish_time, INTERVAL 2 HOUR) <= ?", time.Now().UTC().Truncate(time.Second)).
			Order("CASE WHEN ja.apply_time <= DATE_ADD(j.publish_time, INTERVAL 2 HOUR) THEN 1 ELSE 0 END DESC").
			Order("ja.score DESC").
			Order("ja.apply_time ASC").
			Limit(int(job.NumberOfPeople))
	}

	// 排序字段
	sortKeyList := map[string]string{
		"score": "ja.score",
	}

	if err := builder.
		Group("ja.id").
		Scopes(xresp.AddOrder(sortSet, sortKeyList)).
		Order("ja.apply_time ASC").
		Scopes(xresp.Paginate(pageSet)).
		Find(&resp).Error; err != nil {
		return resp, err
	}

	if len(resp) == 0 {
		return resp, nil
	}

	var languageSectionMap map[string]string
	languageSectionMap, err = SelectionService.GetCodeNameMap(db, []string{model.SelectionTypeLanguage})
	if err != nil {
		return resp, err
	}
	// 處理語言匹配邏輯
	for i := range resp {
		resp[i].MatchedLanguages = s.getMatchedLanguages(job, resp[i].Language)
		if len(resp[i].MatchedLanguages) > 0 {
			languages := strings.Split(resp[i].MatchedLanguages, ",")
			languageNameList := make([]string, 0)
			for _, language := range languages {
				languageNameList = append(languageNameList, languageSectionMap[language])
			}
			if len(languageNameList) > 0 {
				resp[i].MatchedLanguageNames = strings.Join(languageNameList, ",")
			}
		}
	}

	return resp, nil
}

// endregion ---------------------------------------------------- 獲取工作職位申請列表 ----------------------------------------------------

// region ---------------------------------------------------- 機構-獲取已發出的邀請列表 ----------------------------------------------------

// 機構-獲取已發出的邀請列表請求
type JobApplicationInviteListReq struct {
	FacilityId uint64 `form:"facilityId" binding:"required"` // 所屬機構Id
	ReqUserId  uint64 `form:"-"`
}

type JobApplicationInviteListResp struct {
	FacilityId             uint64                                  `json:"facilityId"`                      // 機構Id
	JobId                  uint64                                  `json:"jobId"`                           // 工作Id
	ApplicationId          uint64                                  `json:"applicationId"`                   // 申請Id
	ProfessionalId         uint64                                  `json:"professionalId"`                  // 專業人員Id
	FirstName              string                                  `json:"firstName"`                       // 專業人員名字
	LastName               string                                  `json:"lastName"`                        // 專業人員姓氏
	PositionProfession     string                                  `json:"-"`                               // 專業人員職位
	PositionProfessionName string                                  `json:"positionProfessionName" gorm:"-"` // 專業人員職位名稱
	Score                  int                                     `json:"score"`                           // 匹配分數
	InviteTime             string                                  `json:"inviteTime"`                      // 邀請時間
	ShiftTime              []JobSearchForProfessionalShiftTimeResp `json:"shiftTime" gorm:"-"`              // 班次時間(日曆才返回)
	ServiceLocationAddress string                                  `json:"serviceLocationAddress"`          // 服務地點地址
	Timezone               string                                  `json:"timezone"`                        // 時區
}

// 獲取工作職位申請列表 (機構)
func (s *jobApplicationService) InviteList(db *gorm.DB, req JobApplicationInviteListReq, pageSet *xresp.PageSet, sortSet xresp.SortingSet) ([]JobApplicationInviteListResp, error) {
	var resp []JobApplicationInviteListResp
	var err error
	builder := db.Table("job_application AS ja").
		Joins("JOIN job AS j ON ja.job_id = j.id").
		Joins("JOIN service_location AS sl ON j.service_location_id = sl.id").
		Joins("JOIN professional AS p ON ja.professional_id = p.id").
		Joins("JOIN user AS u ON p.user_id = u.id").
		Select([]string{
			"ja.id AS application_id",
			"ja.facility_id",
			"ja.job_id",
			"ja.professional_id",
			"p.first_name",
			"p.last_name",
			"j.position_profession",
			"ja.score",
			"ja.status",
			"ja.invite_time",
			"IF(sl.address_extra IS NOT NULL AND sl.address_extra != '', CONCAT(sl.address_extra, ' ', sl.address), sl.address) AS service_location_address",
			"sl.timezone",
		}).
		Where("ja.deleted <> ?", model.JobApplicationDeletedY).
		Where("ja.status = ?", model.JobApplicationStatusInvite)

	err = FacilityUserDepartmentService.GetCanAccessDepartmentByUserId(db, req.ReqUserId, func(departments []model.Department) {
		builder = builder.Joins("JOIN job_department AS jd ON jd.job_id = j.id")
		builder = builder.Scopes(FacilityUserDepartmentService.FilterByCanAccessDepartment("jd.department_id", departments))
	})
	if err != nil {
		return nil, err
	}
	if req.FacilityId > 0 {
		builder = builder.Where("ja.facility_id = ?", req.FacilityId)
	}

	// 排序字段
	sortKeyList := map[string]string{
		"inviteTime": "ja.invite_time",
	}

	if err = builder.
		Group("ja.id").
		Scopes(xresp.AddOrder(sortSet, sortKeyList)).
		Order("ja.id ASC").
		Scopes(xresp.Paginate(pageSet)).
		Find(&resp).Error; err != nil {
		return resp, err
	}

	if len(resp) == 0 {
		return resp, nil
	}
	var jobIds []uint64
	for _, item := range resp {
		jobIds = append(jobIds, item.JobId)
	}

	professionSectionMap, err := SelectionService.GetCodeNameMap(db, []string{model.SelectionTypeProfessionalProfession})
	if err != nil {
		return resp, err
	}
	var shiftTimesMap map[uint64][]JobSearchForProfessionalShiftTimeResp
	shiftTimesMap, err = JobShiftTimeService.SetJobShiftTimeList(db, jobIds)
	if err != nil {
		return resp, err
	}
	for i := range resp {
		resp[i].PositionProfessionName = professionSectionMap[resp[i].PositionProfession]
		resp[i].ShiftTime = make([]JobSearchForProfessionalShiftTimeResp, 0)
		resp[i].ShiftTime = shiftTimesMap[resp[i].JobId]
	}

	return resp, nil
}

// endregion ---------------------------------------------------- 機構-獲取已發出的邀請列表 ----------------------------------------------------

// region ---------------------------------------------------- 獲取專業人士符合職位的語言 ----------------------------------------------------

// 獲取專業人士符合職位的語言
func (s *jobApplicationService) getMatchedLanguages(job model.Job, professionalLanguages string) string {
	// 顯示匹配的語言
	if job.Language == "" || professionalLanguages == "" {
		return ""
	}

	// 解析工作要求的語言（逗號分隔）並建立 map
	jobLanguages := strings.Split(job.Language, ",")
	jobLangMap := make(map[string]bool)
	for _, lang := range jobLanguages {
		if lang != "" {
			jobLangMap[lang] = true
		}
	}

	// 解析專業人士的語言並查找匹配
	profLanguages := strings.Split(professionalLanguages, ",")
	var matchedLanguages []string
	for _, profLang := range profLanguages {
		if profLang != "" && jobLangMap[profLang] {
			matchedLanguages = append(matchedLanguages, profLang)
		}
	}

	// 如果沒有匹配的語言，返回空字符串
	if len(matchedLanguages) == 0 {
		return "-"
	}

	// 返回匹配的語言（逗號分隔）
	return strings.Join(matchedLanguages, ", ")
}

// endregion ---------------------------------------------------- 獲取專業人士符合職位的語言 ----------------------------------------------------

// region ---------------------------------------------------- 查詢工作職位申請詳情 ----------------------------------------------------

// JobApplicationInquireReq 查詢工作職位申請詳情請求
type JobApplicationInquireForFacilityReq struct {
	FacilityId       uint64 `form:"facilityId" binding:"required"`       // 所屬機構Id
	JobApplicationId uint64 `form:"jobApplicationId" binding:"required"` // 申請Id
}

func (req *JobApplicationInquireForFacilityReq) GetBaseReq() JobApplicationInquireBaseReq {
	var resp JobApplicationInquireBaseReq
	_ = copier.Copy(&resp, req)
	return resp
}

type JobApplicationInquireForSystemReq struct {
	JobApplicationId uint64 `form:"jobApplicationId" binding:"required"` // 申請Id
}

func (req *JobApplicationInquireForSystemReq) GetBaseReq() JobApplicationInquireBaseReq {
	var resp JobApplicationInquireBaseReq
	_ = copier.Copy(&resp, req)
	return resp
}

type JobApplicationInquireBaseReq struct {
	JobApplicationId uint64
}

// JobApplicationInquireResp 查詢工作職位申請詳情響應
type JobApplicationInquireResp struct {
	ApplicationId       uint64 `json:"applicationId"`       // 申請Id
	FacilityId          uint64 `json:"facilityId"`          // 機構Id
	JobId               uint64 `json:"jobId"`               // 工作Id
	ProfessionalId      uint64 `json:"professionalId"`      // 專業人員Id
	ProfessionalPhotoId uint64 `json:"professionalPhotoId"` // 專業人員照片Id
	Score               int    `json:"score"`               // 匹配分數
	Status              string `json:"status"`              // 申請狀態
	ApplyTime           string `json:"applyTime"`           // 申請時間

	// 專業人員信息
	FirstName            string `json:"firstName"`            // 專業人員名字
	LastName             string `json:"lastName"`             // 專業人員姓氏
	PermissionToWork     string `json:"permissionToWork"`     // 工作許可
	PermissionToWorkName string `json:"permissionToWorkName"` // 工作許可名稱

	Profession     string `json:"profession"`     // 專業
	ProfessionName string `json:"professionName"` // 專業名稱
	Gender         string `json:"gender"`         // 性別
	GenderName     string `json:"genderName"`     // 性別名稱
	DateOfBirth    string `json:"dateOfBirth"`    // 出生日期

	PreferredSpecialities                []ProfessionalPreferredSpecialityDetail                `json:"preferredSpecialities"`                                              // 首選專業
	ExperienceLevel                      string                                                 `json:"experienceLevel"`                                                    // 經驗級別
	ExperienceLevelName                  string                                                 `json:"experienceLevelName"`                                                // 經驗級別名稱
	SupervisionRequirement               string                                                 `json:"supervisionRequirement"`                                             // 監督要求
	SupervisionRequirementName           string                                                 `json:"supervisionRequirementName"`                                         // 監督要求名稱
	AbnNumber                            string                                                 `json:"abnNumber"`                                                          // ABN號碼
	Experiences                          []JobApplicationInquireRespExperience                  `json:"experiences"`                                                        // 工作經驗
	CompletedStudiesInLastThreeYears     string                                                 `json:"completedStudiesInLastThreeYears" binding:"omitempty,oneof=Y N"`     // 過去三年內完成學習 Y/N
	Qualification                        string                                                 `json:"qualification"`                                                      // 學歷
	QualificationName                    string                                                 `json:"qualificationName"`                                                  // 學歷名稱
	QualificationEndDate                 string                                                 `json:"qualificationEndDate" binding:"omitempty,datetime=2006-01-02"`       // 學歷結束日期(YYYY-MM-DD)
	Language                             string                                                 `json:"language"`                                                           // 語言能力
	LanguageName                         string                                                 `json:"languageName"`                                                       // 語言能力名稱
	GraduationInstitution                string                                                 `json:"graduationInstitution"`                                              // 畢業院校
	InstitutionCountry                   string                                                 `json:"institutionCountry"`                                                 // 院校國家
	GraduationYear                       int32                                                  `json:"graduationYear"`                                                     // 畢業年份
	MedicationEndorsement                string                                                 `json:"medicationEndorsement"`                                              // 藥物授權 Y N (僅Enrolled Nurse)
	HasOverseasCitizenshipOrPr           string                                                 `json:"hasOverseasCitizenshipOrPr" binding:"omitempty,oneof=Y N"`           // 是否擁有海外公民身份或永久居留權 Y/N
	RequiresStatutoryDeclaration         string                                                 `json:"requiresStatutoryDeclaration" binding:"omitempty,oneof=Y N"`         // 是否需要法定聲明 Y/N
	HasCompletedInfectionControlTraining string                                                 `json:"hasCompletedInfectionControlTraining" binding:"omitempty,oneof=Y N"` // 是否完成感染控制培訓 Y/N
	FacilityBlacklistId                  uint64                                                 `json:"facilityBlacklistId"`                                                // 機構黑名單Id
	ProfessionalGst                      string                                                 `json:"professionalGst"`                                                    // 專業人士GST
	Files                                map[string][]FacilityAccessProfessionalProfileFileInfo `json:"files" gorm:"-"`                                                     // 文件
	ValidatedFiles                       []string                                               `json:"validatedFiles" gorm:"-"`                                            // 已驗證的文件代碼
}
type JobApplicationInquireRespExperience struct {
	model.ProfessionalExperience
	RoleName          string `json:"roleName"`                    // 角色名稱
	SpecialityName    string `json:"specialityName"`              // 專業名稱
	SubSpecialityName string `json:"subSpecialityName,omitempty"` // 子專業名稱(僅醫生才有)
	GradeName         string `json:"gradeName,omitempty"`         // 級別名稱(僅醫生才有)
}

// 機構訪問專業人員文件類型映射
// true: 機構可以訪問該類型文件
// false: 機構不能訪問該類型文件（隱私保護）
var FacilityAccessProfessionalFileTypeMap = map[string]bool{
	// Part: Personal Information 個人信息
	model.ProfessionalFileCodePhoto: true, // 照片

	// Part: Registration & Certification 註冊與認證
	model.ProfessionalFileCodeAhpraCertificate:              true, // AHPRA證書
	model.ProfessionalFileCodeIndemnityInsuranceCertificate: true, // 專業人士責任保險證明

	// Part: Work Preferences & Experience 工作偏好與經驗
	model.ProfessionalFileCodeQualificationCertificate:                                       true, // 學歷資格證書
	model.ProfessionalFileCodeCurriculumVitae:                                                true, // 專業人士履歷
	model.ProfessionalFileCodePersonalCareWorkQualification:                                  true, // 個人護理工作資格
	model.ProfessionalFileCodePersonalCareWorkerQualificationCertIIIIndividualSupport:        true,
	model.ProfessionalFileCodePersonalCareWorkerQualificationCertificateIIIAGEDCARE:          true,
	model.ProfessionalFileCodePersonalCareWorkerQualificationCertificateIIIHomeCommunityCare: true,
	model.ProfessionalFileCodePersonalCareWorkerQualificationCertIIIDisabilities:             true,
	model.ProfessionalFileCodePersonalCareWorkerQualificationCertIVAgeingSupport:             true,
	model.ProfessionalFileCodePersonalCareWorkerQualificationCertIVDisabilities:              true,
	model.ProfessionalFileCodePersonalCareWorkerQualificationCertificateIVHomeCommunityCare:  true,
	model.ProfessionalFileCodePersonalCareWorkerQualificationBachelorNursing:                 true,
	model.ProfessionalFileCodePersonalCareWorkerQualificationDiplomaNursing:                  true,
	model.ProfessionalFileCodeRegistrarAccreditedEnrolment:                                   true, // Registrar (Accredited) 入學證明
	model.ProfessionalFileCodeFellowshipCertificate:                                          true, // Fellowship證書
	model.ProfessionalFileCodeSpecialistQualification:                                        true, // Specialist資格證明

	// Part: Proof of Identity & Records 身份證明與記錄
	model.ProfessionalFileCodeIdCheck: false, // 專業人士身份證明文件
	// Primary 主要身份證明文件
	model.ProfessionalFileCodeAustralianPassport:               false, // 澳洲護照
	model.ProfessionalFileCodeForeignPassport:                  false, // 外國護照
	model.ProfessionalFileCodeAustralianBirthCertificate:       false, // 澳洲出生證明
	model.ProfessionalFileCodeAustralianCitizenshipCertificate: false, // 澳洲公民證
	// Secondary 次要身份證明文件
	model.ProfessionalFileCodeCurrentAustraliaDriverLicence:         false, // 澳洲駕照
	model.ProfessionalFileCodeAustralianPublicServiceEmployeeIDCard: false, // 澳洲公務員ID卡
	model.ProfessionalFileCodeOtherAustralianGovernmentIssueIDCard:  false, // 其他澳洲政府發出的ID卡
	model.ProfessionalFileCodeTertiaryStudentIDCard:                 false, // 大學生ID卡
	// Others 其他身份證明文件
	model.ProfessionalFileCodeCreditDebitAtmCard:                false, // 信用卡/扣帳卡/ATM卡
	model.ProfessionalFileCodeMedicareCard:                      false, // 醫療卡
	model.ProfessionalFileCodeUtilityBillOrRateNotice:           false, // 水電費單或收費通知
	model.ProfessionalFileCodeStatementFromFinancialInstitution: false, // 金融機構的結單
	model.ProfessionalFileCodeCentrelinkOrPensionCard:           false, // 澳洲国民福利署或養老金卡
	// Work Authorization & Background Checks 工作授權與背景檢查
	model.ProfessionalFileCodeVisa:                                  false, // 簽證
	model.ProfessionalFileCodeNationalCriminalCheck:                 false, // 國家犯罪檢查
	model.ProfessionalFileCodeWorkingWithChildrenOrVulnerablePeople: false, // 兒童/脆弱人群工作檢查
	model.ProfessionalFileCodeCurrentImmunisationRecords:            false, // 現在的免疫記錄
	model.ProfessionalFileCodeCommonwealthStatutoryDeclaration:      false, // 聯邦法定聲明

	// Part: Additional Certification 附加證明
	model.ProfessionalFileCodeAdditionalCertification: true, // 附加證明
	model.ProfessionalFileCodeDisclosure:              true, // 披露
}

// 專業人員文件信息
type FacilityAccessProfessionalProfileFileInfo struct {
	ProfessionalFiles []FacilityAccessProfessionalProfileFileDetail `json:"professionalFiles"` // 文件信息
	FileCodeName      string                                        `json:"fileCodeName,omitempty"`
	Description       string                                        `json:"description,omitempty"` // 描述
}
type FacilityAccessProfessionalProfileFileDetail struct {
	ProfessionalFileId uint64 `json:"professionalFileId"` // 文件ID
	UuidName           string `json:"filename"`           // UUID文件名稱
}

// Inquire 機構查詢工作職位申請詳情
func (s *jobApplicationService) Inquire(db *gorm.DB, req JobApplicationInquireBaseReq) (JobApplicationInquireResp, error) {
	var resp JobApplicationInquireResp

	// 查詢申請記錄
	var application model.JobApplication
	if err := db.First(&application, req.JobApplicationId).Error; err != nil {
		return resp, err
	}

	// 複製基本申請信息
	_ = copier.Copy(&resp, application)
	resp.ApplicationId = application.Id
	resp.ApplyTime = application.ApplyTime.Format(xtool.DateTimeSecA1)

	// 查詢專業人員信息
	profile, err := ProfessionalProfileService.Inquire(db, ProfessionalProfileInquireReq{
		ProfessionalId: application.ProfessionalId,
	})
	if err != nil {
		return resp, err
	}

	// 複製專業人員信息 忽略狀態
	err = xtool.CopyWithIgnoreFieldNames(&resp, profile, "Status")
	if err != nil {
		return resp, err
	}

	// 過濾機構訪問專業人員文件類型
	validatedFiles := make([]string, 0)
	if profile.IdCheckFileTypes != "" {
		validatedFiles = strings.Split(profile.IdCheckFileTypes, ",") // 已驗證的文件
	}
	for key := range resp.Files {
		if canAccess, exist := FacilityAccessProfessionalFileTypeMap[key]; !exist || !canAccess {
			if exist {
				validatedFiles = append(validatedFiles, key)
			}
			delete(resp.Files, key)
		}
	}
	resp.ValidatedFiles = validatedFiles

	// 獲取各種選項的名稱映射
	specialityCodes := make([]string, 0)
	for _, speciality := range resp.PreferredSpecialities {
		if resp.Profession == model.ProfessionalProfessionMedicalPractitioner {
			if speciality.Speciality != "" {
				specialityCodes = append(specialityCodes, speciality.Speciality)
			}
			if speciality.SubSpeciality != "" {
				specialityCodes = append(specialityCodes, speciality.SubSpeciality)
			}
		} else {
			specialities := strings.Split(speciality.Speciality, ",")
			specialityCodes = append(specialityCodes, specialities...)
		}
	}
	for _, exp := range resp.Experiences {
		if resp.Profession == model.ProfessionalProfessionMedicalPractitioner {
			if exp.Speciality != "" {
				specialityCodes = append(specialityCodes, exp.Speciality)
			}
			if exp.SubSpeciality != "" {
				specialityCodes = append(specialityCodes, exp.SubSpeciality)
			}
		} else {
			specialities := strings.Split(exp.Speciality, ",")
			specialityCodes = append(specialityCodes, specialities...)
		}
	}
	specialityMap, err := SelectionService.GetMapByCodes(db, specialityCodes)
	if err != nil {
		return resp, err
	}
	var preferredGradeSectionMap map[string]string
	preferredGradeSectionMap, err = SelectionService.GetCodeNameMap(db, []string{model.SelectionTypePreferredGrade})
	if err != nil {
		return resp, err
	}

	var professionSectionMap map[string]string
	professionSectionMap, err = SelectionService.GetCodeNameMap(db, []string{model.SelectionTypeProfessionalProfession})
	if err != nil {
		return resp, err
	}
	var personalCareWorkQualificationSectionMap map[string]string
	personalCareWorkQualificationSectionMap, err = SelectionService.GetCodeNameMap(db, []string{model.ProfessionalFileCodePersonalCareWorkQualification})
	if err != nil {
		return resp, err
	}
	if resp.Files != nil {
		for key, files := range resp.Files {
			if name, exist := personalCareWorkQualificationSectionMap[key]; exist {
				for i, _ := range files {
					resp.Files[key][i].FileCodeName = name
				}
			}
		}
	}

	if profile.Language != "" {
		languages := strings.Split(profile.Language, ",")
		var languageSectionMap map[string]string
		languageSectionMap, err = SelectionService.GetCodeNameMap(db, []string{model.SelectionTypeLanguage})
		if err != nil {
			return resp, err
		}
		languageNameList := make([]string, 0)
		for _, language := range languages {
			languageNameList = append(languageNameList, languageSectionMap[language])
		}
		if len(languageNameList) > 0 {
			resp.LanguageName = strings.Join(languageNameList, ",")
		}
	}

	// 補充名稱
	if resp.PermissionToWork != "" {
		var permissionToWorkMap map[string]string
		permissionToWorkMap, err = SelectionService.GetCodeNameMap(db, []string{model.SelectionTypeProfessionalPermissionToWork})
		if err != nil {
			return resp, err
		}
		resp.PermissionToWorkName = permissionToWorkMap[resp.PermissionToWork]
	}
	if resp.Profession != "" {
		resp.ProfessionName = professionSectionMap[resp.Profession]
	}
	if resp.Gender != "" {
		var genderSectionMap map[string]string
		genderSectionMap, err = SelectionService.GetCodeNameMap(db, []string{model.SelectionTypeGender})
		if err != nil {
			return resp, err
		}
		resp.GenderName = genderSectionMap[resp.Gender]
	}
	if resp.ExperienceLevel != "" {
		var experienceLevelSectionMap map[string]string
		experienceLevelSectionMap, err = SelectionService.GetMapByCodes(db, []string{
			resp.ExperienceLevel,
		})
		if err != nil {
			return resp, err
		}
		resp.ExperienceLevelName = experienceLevelSectionMap[resp.ExperienceLevel]
	}
	if resp.SupervisionRequirement != "" {
		var supervisionRequirementMap map[string]string
		supervisionRequirementMap, err = SelectionService.GetCodeNameMap(db, []string{model.SelectionTypeSupervisionRequirement})
		if err != nil {
			return resp, err
		}
		resp.SupervisionRequirementName = supervisionRequirementMap[resp.SupervisionRequirement]
	}

	// 處理首選專業名稱
	if len(resp.PreferredSpecialities) > 0 {
		for i := range resp.PreferredSpecialities {
			resp.PreferredSpecialities[i].SpecialityName = specialityMap[resp.PreferredSpecialities[i].Speciality]
			if resp.PreferredSpecialities[i].SubSpeciality != "" {
				resp.PreferredSpecialities[i].SubSpecialityName = specialityMap[resp.PreferredSpecialities[i].SubSpeciality]
			}
			if resp.PreferredSpecialities[i].Grade != "" {
				resp.PreferredSpecialities[i].GradeName = preferredGradeSectionMap[resp.PreferredSpecialities[i].Grade]
			}
		}
	}

	// 處理工作經驗的名稱
	for i, exp := range resp.Experiences {
		if exp.Role != "" {
			exp.RoleName = professionSectionMap[exp.Role]
		}

		if exp.Speciality != "" {
			exp.SpecialityName = specialityMap[exp.Speciality]
		}
		if exp.SubSpeciality != "" {
			exp.SubSpecialityName = specialityMap[exp.SubSpeciality]
		}
		if resp.Profession != model.ProfessionalProfessionMedicalPractitioner {
			specialities := strings.Split(exp.Speciality, ",")
			names := make([]string, 0)
			for _, speciality := range specialities {
				names = append(names, specialityMap[speciality])
			}
			exp.SpecialityName = strings.Join(names, ",")
		}
		if exp.Grade != "" {
			exp.GradeName = preferredGradeSectionMap[exp.Grade]
		}
		resp.Experiences[i] = exp
	}

	// 該專業人士是否在黑名單中
	var blacklist model.FacilityBlacklist
	if err = db.
		Where("facility_id = ?", resp.FacilityId).
		Where("user_id = ?", profile.UserId).
		Select("id").
		First(&blacklist).Error; xgorm.IsSqlErr(err) {
		return resp, err
	}
	if blacklist.Id > 0 {
		resp.FacilityBlacklistId = blacklist.Id
	}

	var job model.Job
	if err = db.
		Where("id = ?", resp.JobId).
		First(&job).Error; xgorm.IsSqlErr(err) {
		return resp, err
	}
	var serviceLocation model.ServiceLocation
	if err = db.
		Where("facility_id = ?", resp.FacilityId).
		Where("id = ?", job.ServiceLocationId).
		First(&serviceLocation).Error; xgorm.IsSqlErr(err) {
		return resp, err
	}
	tz, err := time.LoadLocation(serviceLocation.Timezone)
	if err != nil {
		return resp, err
	}
	beginDate := job.BeginTime.In(tz).Format(xtool.DateDayA)

	// 查詢專業人士GST
	gst, err := ProfessionalGstService.GetProfessionalGst(db, ProfessionalGstQueryReq{
		ProfessionalId: profile.ProfessionalId,
		QueryDate:      beginDate,
	})
	if err != nil {
		return resp, err
	}
	resp.ProfessionalGst = "N"
	if gst != nil {
		resp.ProfessionalGst = "Y"
	}

	return resp, nil
}

// endregion ---------------------------------------------------- 查詢工作職位申請詳情 ----------------------------------------------------

// region ---------------------------------------------------- 工作職位申請列表-專業人士 ----------------------------------------------------

// 工作職位申請列表-專業人士請求
type MyJobListReq struct {
	FacilityId        uint64          `form:"facilityId"`                                                                                                                                                                           // 所屬機構Id
	Address           string          `form:"address"`                                                                                                                                                                              // 地址
	BeginTime         string          `form:"beginTime" binding:"omitempty,datetime=2006-01-02 15:04:05"`                                                                                                                           // 工作開始時間 (YYYY-MM-DD HH:MM:SS)
	EndTime           string          `form:"endTime" binding:"omitempty,datetime=2006-01-02 15:04:05"`                                                                                                                             // 工作結束時間 (YYYY-MM-DD HH:MM:SS)
	MinHourlyRate     decimal.Decimal `form:"minHourlyRate"`                                                                                                                                                                        // 最低時薪
	Status            string          `form:"status" binding:"required_without=Progress,omitempty,splitin=APPLY INVITE DECLINE ACCEPT APPLICATION_CANCEL WITHDRAW CHATTING APPLICATION_CANCEL FACILITY_CANCEL PROFESSIONAL_CANCEL"` // 職位狀態,可逗號分隔 已申請=APPLY,已邀請=INVITE,已婉拒=DECLINE,已接受=ACCEPT,已取消=APPLICATION_CANCEL,已撤銷=WITHDRAW,聊天中=CHATTING,機構已取消=FACILITY_CANCEL APPLICATION_CANCEL,應聘者已取消=PROFESSIONAL_CANCEL
	Progress          string          `form:"progress" binding:"required_without=Status,omitempty,oneof=UPCOMING IN_PROGRESS APPLIED COMPLETE CANCEL"`                                                                              // 進度篩選 UPCOMING=待開始 IN_PROGRESS=進行中 APPLIED=已申請 COMPLETE=已完成 CANCEL=已取消
	JobStatus         string          `form:"jobStatus" binding:"omitempty,oneof=NO_CONFIRMATION NO_INVOICE UNPAID PARTIALLY_PAID FULLY_PAID CHATTING PENDING DECLINED CONFIRMED APPLIED_ONLY"`                                     // 工作狀態篩選 NO_CONFIRMATION=未開立確認單 NO_INVOICE=未開立發票 UNPAID=未付款 PARTIALLY_PAID=部分付款 FULLY_PAID=已付款 CHATTING=聊天中 PENDING=待開始 CONFIRMED=已確認 APPLIED_ONLY=只申請
	HideApplied       string          `form:"hideApplied" binding:"omitempty,oneof=Y N"`                                                                                                                                            // 是否隱藏APPLIED狀態的記錄 Y/N
	ReqUserId         uint64          `form:"-"`                                                                                                                                                                                    // 請求者Id
	CalendarBeginTime string          `form:"calendarBeginTime" binding:"omitempty,datetime=2006-01-02 15:04:05"`                                                                                                                   // 日曆開始時間(日曆才用) (YYYY-MM-DD HH:MM:SS)
	CalendarEndTime   string          `form:"calendarEndTime" binding:"omitempty,datetime=2006-01-02 15:04:05"`                                                                                                                     // 日曆結束時間(日曆才用) (YYYY-MM-DD HH:MM:SS)
	NeedShiftTime     string          `form:"needShiftTime" binding:"omitempty,oneof=Y N"`                                                                                                                                          // 是否需要班次時間 Y/N
	UninvoicedShift   string          `form:"uninvoicedShift" binding:"omitempty,oneof=Y N"`                                                                                                                                        // 包含顯示未開確認單的工作 Y/N(僅progress=COMPLETE有效)
	Timezone          string          `form:"timeZone" binding:"omitempty,timezone"`                                                                                                                                                // 時區 如果沒有默認已經是機構的時區
}

// 工作職位申請列表-系統請求
type MyJobListReqBySystem struct {
	UserId            uint64          `form:"userId" binding:"required"`                                                                                                                                // 請求者Id
	FacilityId        uint64          `form:"facilityId"`                                                                                                                                               // 所屬機構Id
	Address           string          `form:"address"`                                                                                                                                                  // 地址
	BeginTime         string          `form:"beginTime" binding:"omitempty,datetime=2006-01-02 15:04:05"`                                                                                               // 工作開始時間 (YYYY-MM-DD HH:MM:SS)
	EndTime           string          `form:"endTime" binding:"omitempty,datetime=2006-01-02 15:04:05"`                                                                                                 // 工作結束時間 (YYYY-MM-DD HH:MM:SS)
	MinHourlyRate     decimal.Decimal `form:"minHourlyRate"`                                                                                                                                            // 最低時薪
	Status            string          `form:"status" binding:"omitempty,oneof=APPLY INVITE DECLINE ACCEPT APPLICATION_CANCEL WITHDRAW CHATTING APPLICATION_CANCEL FACILITY_CANCEL PROFESSIONAL_CANCEL"` // 職位狀態,可逗號分隔 已申請=APPLY,已邀請=INVITE,已婉拒=DECLINE,已接受=ACCEPT,已取消=APPLICATION_CANCEL,已撤銷=WITHDRAW,聊天中=CHATTING,機構已取消=FACILITY_CANCEL APPLICATION_CANCEL,應聘者已取消=PROFESSIONAL_CANCEL
	Progress          string          `form:"progress" binding:"omitempty,oneof=UPCOMING IN_PROGRESS APPLIED COMPLETE CANCEL"`                                                                          // 進度篩選 UPCOMING=待開始 IN_PROGRESS=進行中 APPLIED=已申請 COMPLETE=已完成 CANCEL=已取消
	JobStatus         string          `form:"jobStatus" binding:"omitempty,oneof=NO_CONFIRMATION NO_INVOICE UNPAID PARTIALLY_PAID FULLY_PAID CHATTING PENDING CONFIRMED APPLIED_ONLY"`                  // 工作狀態篩選 NO_CONFIRMATION=未開立確認單 NO_INVOICE=未開立發票 UNPAID=未付款 PARTIALLY_PAID=部分付款 FULLY_PAID=已付款 CHATTING=聊天中 PENDING=待開始 CONFIRMED=已確認 APPLIED_ONLY=只申請
	HideApplied       string          `form:"hideApplied" binding:"omitempty,oneof=Y N"`                                                                                                                // 是否隱藏APPLIED狀態的記錄 Y/N
	CalendarBeginTime string          `form:"calendarBeginTime" binding:"omitempty,datetime=2006-01-02 15:04:05"`                                                                                       // 日曆開始時間(日曆才用) (YYYY-MM-DD HH:MM:SS)
	CalendarEndTime   string          `form:"calendarEndTime" binding:"omitempty,datetime=2006-01-02 15:04:05"`                                                                                         // 日曆結束時間(日曆才用) (YYYY-MM-DD HH:MM:SS)
	NeedShiftTime     string          `form:"needShiftTime" binding:"omitempty,oneof=Y N"`
	UninvoicedShift   string          `form:"uninvoicedShift" binding:"omitempty,oneof=Y N"` // 包含顯示未開確認單的工作 Y/N(僅progress=COMPLETE有效)
}

func (req *MyJobListReqBySystem) GetBaseReq() MyJobListReq {
	var resp MyJobListReq
	_ = copier.Copy(&resp, req)
	resp.ReqUserId = req.UserId
	return resp

}

// 工作職位申請列表-響應
type MyJobListResp struct {
	JobApplicationId       uint64                                  `json:"jobApplicationId"`            // 工作申請Id
	JobId                  uint64                                  `json:"jobId"`                       // 工作Id
	FacilityId             uint64                                  `json:"facilityId"`                  // 所屬機構Id
	FacilityName           string                                  `json:"facilityName"`                // 機構名稱
	ServiceLocationAddress string                                  `json:"serviceLocationAddress"`      // 服務地點地址
	BeginTime              string                                  `json:"beginTime"`                   // 工作開始時間 (YYYY-MM-DD HH:MM:SS)
	EndTime                string                                  `json:"endTime"`                     // 工作結束時間 (YYYY-MM-DD HH:MM:SS)
	MinHourlyRate          decimal.Decimal                         `json:"minHourlyRate"`               // 最低時薪
	MaxHourlyRate          decimal.Decimal                         `json:"maxHourlyRate"`               // 最高時薪
	Duration               decimal.Decimal                         `json:"duration"`                    // 總工作時長
	CancelReason           string                                  `json:"cancelReason"`                // 取消原因
	CalendarNote           string                                  `json:"calendarNote"`                // 日曆備註
	ShiftTime              []JobSearchForProfessionalShiftTimeResp `json:"shiftTime" gorm:"-"`          // 班次時間(日曆才返回)
	Progress               string                                  `json:"progress,omitempty" gorm:"-"` // 進度 UPCOMING=待開始 IN_PROGRESS=進行中 APPLIED=已申請 COMPLETE=已完成 CANCEL=已取消
	Timezone               string                                  `json:"timezone"`                    // 時區
	Status                 string                                  `json:"status"`                      // 職位狀態
	PayHours               decimal.Decimal                         `json:"payHours"`                    // 工作時數
	UninvoicedShift        string                                  `json:"uninvoicedShift"`             // 是否有未開確認單的JobShift Y/N
	ExpRecAmount           decimal.Decimal                         `json:"expRecAmount" gorm:"-"`       // 該工作預計的總收入金額
	InvoiceAmount          decimal.Decimal                         `json:"invoiceAmount" gorm:"-"`      // 該工作應收金額
	UnpaidAmount           decimal.Decimal                         `json:"unpaidAmount" gorm:"-"`       // 該工作未收金額
	JobStatus              string                                  `json:"jobStatus" gorm:"-"`          // 工作狀態
	ProfessionalNames      string                                  `json:"professionalNames" gorm:"-"`  // 已接受專業人士姓名
	Specialisation         string                                  `json:"specialisation"`              // 專業編號
	SpecialisationName     string                                  `json:"specialisationName" gorm:"-"` // 專業名稱
	JobCurrentStatus       string                                  `json:"-"`                           // 工作當前狀態
	Accept                 string                                  `json:"-"`                           // 是否取錄
	ConfirmationNotesCount int                                     `json:"confirmationNotesCount"`      // 確認單數量（已確認、已發送、已拒絕）
	InviteTime             *time.Time                              `json:"inviteTime,omitempty"`        // 邀請時間(僅INVITE狀態返回)
}

// 根據申請狀態和時間計算工作申請進度
func calculateJobApplicationProgress(jobStatus string, status string, beginTime, endTime time.Time, timezone string) string {
	if jobStatus == model.JobStatusCancel {
		return JobApplicationProgressCancel
	}
	if timezone == "" {
		return ""
	}
	tz, err := time.LoadLocation(timezone)
	if err != nil {
		return ""
	}
	nowTime := time.Now().In(tz)

	// 根據 my-job-tab.md 文檔定義的五個標籤
	switch status {
	case model.JobApplicationStatusApply, model.JobApplicationStatusChatting, model.JobApplicationStatusInvite:
		// Applied: 申請中、聊天中或被邀請，且工作結束時間大於當前時間
		if endTime.After(nowTime) {
			return JobApplicationProgressApplied
		}
	case model.JobApplicationStatusAccept:
		// Upcoming: 已接受且工作開始時間大於當前時間
		if beginTime.After(nowTime) {
			return JobApplicationProgressUpcoming
		} else if endTime.After(nowTime) {
			// In Progress: 已接受且當前時間在工作開始和結束時間之間
			return JobApplicationProgressInProgress
		} else {
			// Completed: 已接受且工作結束時間小於當前時間
			return JobApplicationProgressComplete
		}
	case model.JobApplicationStatusApplicationCancel, model.JobApplicationStatusFacilityCancel, model.JobApplicationStatusProfessionalCancel:
		// Cancelled: 機構已取消或專業人員已取消
		return JobApplicationProgressCancel
	}

	// 默認返回空字符串
	return ""
}

// 工作職位申請列表-專業人士
func (s *jobApplicationService) MyJobList(db *gorm.DB, req MyJobListReq, pageSet *xresp.PageSet, sortSet xresp.SortingSet) ([]MyJobListResp, error) {
	var resp []MyJobListResp
	var err error

	confirmationNoteBuilder := db.Model(&model.Document{}).
		Table("document as d").
		Select([]string{
			"d.job_application_id",
			"COUNT(*) as confirmation_notes_count",
		}).
		Where("d.category = ?", model.DocumentCategoryConfirmation).
		Where("d.data_type <> ?", model.DocumentDataTypeSystemToFacility).
		Where("d.progress in (?)", []string{model.DocumentProgressConfirm, model.DocumentProgressSent, model.DocumentProgressReject}).
		Group("d.job_application_id")

	if req.FacilityId > 0 {
		confirmationNoteBuilder.Where("d.facility_id = ?", req.FacilityId)
	}

	// 構建未開確認單的JobShift查詢
	uninvoicedShiftBuilder := s.buildUninvoicedShiftQuery(db, req.ReqUserId)

	builder := db.Table("job_application AS ja").
		Select([]string{
			"ja.id AS job_application_id",
			"ja.job_id",
			"j.facility_id",
			"fp.name AS facility_name",
			"IF(sl.address_extra IS NOT NULL AND sl.address_extra != '', CONCAT(sl.address_extra, ' ', sl.address), sl.address) AS service_location_address",
			"j.begin_time",
			"j.end_time",
			"j.min_hourly_rate",
			"j.max_hourly_rate",
			"j.duration",
			"j.pay_hours",
			"j.specialisation",
			fmt.Sprintf("CASE WHEN ja.status = '%s' THEN ja.invite_time ELSE NULL END AS invite_time", model.JobApplicationStatusInvite),
			"ja.calendar_note",
			"ja.status",
			"ja.cancel_reason",
			"sl.timezone AS timezone",
			"j.status AS job_current_status",
			"ja.accept",
			"COALESCE(cn.confirmation_notes_count, 0) AS confirmation_notes_count",
			"CASE WHEN us.job_id IS NOT NULL THEN 'Y' ELSE 'N' END AS uninvoiced_shift",
		}).
		Joins("JOIN job j ON j.id = ja.job_id").
		Joins("JOIN facility_profile fp ON fp.facility_id = j.facility_id AND fp.data_type = ?", model.FacilityProfileDataTypeApproved).
		Joins("JOIN service_location sl ON j.service_location_id = sl.id").
		Joins("LEFT JOIN (?) AS cn ON cn.job_application_id = ja.id", confirmationNoteBuilder).
		Joins("LEFT JOIN (?) AS us ON us.job_id = ja.job_id", uninvoicedShiftBuilder).
		Where("ja.user_id = ?", req.ReqUserId).
		Where("ja.status <> ?", model.JobApplicationStatusWithdraw).
		Where("ja.deleted <> ?", model.JobApplicationDeletedY)

	tz := time.UTC
	if req.Timezone != "" {
		tz, err = time.LoadLocation(req.Timezone)
		if err != nil {
			return nil, err
		}
	}

	if req.FacilityId > 0 {
		builder = builder.Where("j.facility_id = ?", req.FacilityId)
	}

	if req.Address != "" {
		builder = builder.Where("IF(sl.address_extra IS NOT NULL AND sl.address_extra != '', CONCAT(sl.address_extra, ' ', sl.address), sl.address) LIKE ?", xgorm.EscapeLikeWithWildcards(req.Address))
	}

	if req.BeginTime != "" {
		if req.Timezone == "" {
			builder = builder.Where(xgorm.ConvertTZSql("j.begin_time", "sl.timezone", ">="), req.BeginTime)
		} else {
			beginTime, err := time.ParseInLocation(xtool.DateTimeSecA1, req.BeginTime, tz)
			if err != nil {
				return nil, err
			}
			builder = builder.Where("j.begin_time >= ?", beginTime.UTC())
		}
	}

	if req.EndTime != "" {
		if req.Timezone == "" {
			builder = builder.Where(xgorm.ConvertTZSql("j.end_time", "sl.timezone", "<="), req.EndTime)
		} else {
			endTime, err := time.ParseInLocation(xtool.DateTimeSecA1, req.EndTime, tz)
			if err != nil {
				return nil, err
			}
			builder = builder.Where("j.end_time <= ?", endTime.UTC())
		}
	}

	if !req.MinHourlyRate.IsZero() {
		hourRate, ok := req.MinHourlyRate.Float64()
		if !ok {
			return resp, errors.New("invalid hourly rate")
		}
		builder = builder.Where("j.min_hourly_rate >= ?", hourRate)
	}

	if req.Status != "" {
		builder = builder.Where("ja.status IN (?)", strings.Split(req.Status, ","))
	}
	if req.JobStatus != "" {
		switch req.JobStatus {
		case "NO_CONFIRMATION":
			// 未開立確認單
			builder = builder.Joins("LEFT JOIN document AS d ON d.job_application_id = ja.id AND d.category = ?", model.DocumentCategoryConfirmation).
				Where("d.id IS NULL")
		case "NO_INVOICE":
			// 未開立發票
			builder = builder.Joins("LEFT JOIN document AS d ON d.job_application_id = ja.id AND d.category = ?", model.DocumentCategoryInvoice).
				Where("d.id IS NULL")
		case "UNPAID":
			// 有未收款發票
			builder = builder.Joins("LEFT JOIN document AS d_unpaid ON d_unpaid.job_application_id = ja.id AND d_unpaid.category = ? AND d_unpaid.data_type <> ?", model.DocumentCategoryInvoice, model.DocumentDataTypeSystemToFacility).
				Where("d_unpaid.id IS NOT NULL AND d_unpaid.payment_received = ?", model.DocumentProgressPaymentReceivedN)
		case "PARTIALLY_PAID":
			// 有未收款發票也有已收款發票
			invoiceBuilder := db.Model(&model.Document{}).
				Table("document AS d").
				Select("d.job_application_id, COUNT(*) as total_invoices, SUM(CASE WHEN d.payment_received = ? THEN 1 ELSE 0 END) as paid_invoices",
					model.DocumentProgressPaymentReceivedY).
				Where("d.category = ?", model.DocumentCategoryInvoice).
				Where("d.data_type < ?", model.DocumentDataTypeSystemToFacility).
				Where("d.progress = ?", model.DocumentProgressConfirm).
				Group("d.job_application_id").
				Having("paid_invoices > 0 AND paid_invoices < total_invoices")
			builder = builder.
				Joins("JOIN (?) as d_partially_paid ON d_partially_paid.job_application_id = ja.id", invoiceBuilder)
		case "FULLY_PAID":
			// 所有發票都已收款
			invoiceBuilder := db.Model(&model.Document{}).
				Table("document AS d").
				Select("d.job_application_id, COUNT(*) as total_invoices, SUM(CASE WHEN d.payment_received = ? THEN 1 ELSE 0 END) as paid_invoices",
					model.DocumentProgressPaymentReceivedY).
				Where("d.category = ?", model.DocumentCategoryInvoice).
				Where("d.data_type < ?", model.DocumentDataTypeSystemToFacility).
				Where("d.progress = ?", model.DocumentProgressConfirm).
				Group("d.job_application_id").
				Having("paid_invoices = total_invoices AND total_invoices > 0")
			builder = builder.
				Joins("JOIN (?) as d_fully_paid ON d_fully_paid.job_application_id = ja.id", invoiceBuilder)
		case "CHATTING":
			builder = builder.Where("ja.status IN (?, ?)", model.JobApplicationStatusChatting, model.JobApplicationStatusInvite)
		case "PENDING":
			builder = builder.Where("ja.status = ?", model.JobApplicationStatusApply)
		case "DECLINED":
			builder = builder.Where("ja.status = ?", model.JobApplicationStatusDecline)
		case "APPLIED_ONLY":
			builder = builder.Where("ja.status IN (?)", []string{model.JobApplicationStatusApplicationCancel, model.JobApplicationStatusFacilityCancel, model.JobApplicationStatusProfessionalCancel}).
				Where("ja.accept <> ?", model.JobApplicationAcceptY)
		case "CONFIRMED":
			builder = builder.Where("ja.status IN (?)", []string{model.JobApplicationStatusApplicationCancel, model.JobApplicationStatusFacilityCancel, model.JobApplicationStatusProfessionalCancel}).
				Where("ja.accept = ?", model.JobApplicationAcceptY)
			if req.UninvoicedShift == "Y" {
				builder = builder.Where("us.job_id IS NOT NULL")
			}
		}
	}

	if req.CalendarBeginTime != "" || req.CalendarEndTime != "" {
		// 日曆篩選，不包含已取消的工作
		builder = builder.Where("ja.status NOT IN (?)", []string{model.JobApplicationStatusApplicationCancel, model.JobApplicationStatusFacilityCancel, model.JobApplicationStatusProfessionalCancel})
		if req.CalendarEndTime != "" {
			if req.Timezone == "" {
				builder = builder.Where(xgorm.ConvertTZSql("j.begin_time", "sl.timezone", "<"), req.CalendarEndTime)
			} else {
				endTime, err := time.ParseInLocation(xtool.DateTimeSecA1, req.CalendarEndTime, tz)
				if err != nil {
					return nil, err
				}
				builder = builder.Where("j.begin_time < ?", endTime.UTC())
			}
		}
		if req.CalendarBeginTime != "" {
			if req.Timezone == "" {
				builder = builder.Where(xgorm.ConvertTZSql("j.end_time", "sl.timezone", ">"), req.CalendarBeginTime)
			} else {
				beginTime, err := time.ParseInLocation(xtool.DateTimeSecA1, req.CalendarBeginTime, tz)
				if err != nil {
					return nil, err
				}
				builder = builder.Where("j.end_time > ?", beginTime.UTC())
			}
		}
	}

	// 根據 Progress 進行篩選，參照 my-job-tab.md 文檔
	nowStr := time.Now().UTC().Format(xtool.DateTimeSecA1)
	if req.Progress != "" {
		switch req.Progress {
		case JobApplicationProgressUpcoming:
			// Upcoming: 已接受且工作開始時間大於當前時間
			builder = builder.Where("j.status = ?", model.JobStatusPublish).Where("ja.status = ? AND j.begin_time > ?", model.JobApplicationStatusAccept, nowStr)
		case JobApplicationProgressInProgress:
			// In Progress: 已接受且當前時間在工作開始和結束時間之間
			builder = builder.Where("j.status = ?", model.JobStatusPublish).Where("ja.status = ? AND j.begin_time <= ? AND j.end_time >= ?", model.JobApplicationStatusAccept, nowStr, nowStr)
		case JobApplicationProgressApplied:
			// Applied: 申請中、聊天中或被邀請 且 工作開始時間-1小時大於當前時間
			builder = builder.Where("j.status = ?", model.JobStatusPublish).
				Where("ja.status IN (?)", []string{model.JobApplicationStatusApply, model.JobApplicationStatusChatting, model.JobApplicationStatusInvite, model.JobApplicationStatusDecline})
			//Where("DATE_SUB(j.begin_time, INTERVAL 1 HOUR) > ?", nowStr)
		case JobApplicationProgressComplete:
			// Completed: 已接受且工作結束時間小於當前時間
			builder = builder.Where("j.status = ?", model.JobStatusPublish).
				Where("ja.status = ? AND j.end_time < ?", model.JobApplicationStatusAccept, nowStr)
		case JobApplicationProgressCancel:
			// Cancelled: 機構已取消或專業人員已取消
			builder = builder.Where("j.status = ? OR ja.status IN (?)", model.JobStatusCancel, []string{model.JobApplicationStatusApplicationCancel, model.JobApplicationStatusFacilityCancel, model.JobApplicationStatusProfessionalCancel}).
				Where("(ja.accept = ?) OR (ja.accept = ? AND j.status = ?)", model.JobApplicationAcceptY, model.JobApplicationAcceptN, model.JobStatusCancel)
		}
	}

	// 處理 HideApplied 字段
	if req.HideApplied == "Y" {
		builder = builder.Where("ja.status NOT IN (?)", []string{model.JobApplicationStatusApply, model.JobApplicationStatusChatting, model.JobApplicationStatusInvite, model.JobApplicationStatusDecline}).
			Where("(ja.accept = ?) OR (ja.accept = ? AND j.status = ?)", model.JobApplicationAcceptY, model.JobApplicationAcceptN, model.JobStatusCancel)
	}

	sortKeyList := map[string]string{
		"beginTime":  "j.begin_time",
		"applyTime":  "ja.apply_time",
		"hourlyRate": "j.max_hourly_rate", // 按最高時薪降序
	}

	if err = builder.
		Scopes(xresp.AddOrder(sortSet, sortKeyList)).
		Group("ja.id").
		Order("ja.id DESC").
		Scopes(xresp.Paginate(pageSet)).
		Find(&resp).Error; err != nil {
		return resp, err
	}

	var jobIds []uint64
	var jobApplicationIds []uint64
	var specialisationList []string
	for i, item := range resp {
		jobIds = append(jobIds, item.JobId)
		specialisationList = append(specialisationList, item.Specialisation)
		jobApplicationIds = append(jobApplicationIds, item.JobApplicationId)
		resp[i].ShiftTime = make([]JobSearchForProfessionalShiftTimeResp, 0)

		// 計算Progress字段
		beginTime, err := time.Parse(xtool.DateTimeSecA1, item.BeginTime)
		if err != nil {
			continue
		}
		endTime, err := time.Parse(xtool.DateTimeSecA1, item.EndTime)
		if err != nil {
			continue
		}

		// 使用獨立方法計算進度
		resp[i].Progress = calculateJobApplicationProgress(item.JobCurrentStatus, item.Status, beginTime, endTime, item.Timezone)
	}

	if len(resp) > 0 {
		var shiftTimesMap map[uint64][]JobSearchForProfessionalShiftTimeResp
		if req.NeedShiftTime == "Y" {
			shiftTimesMap, err = JobShiftTimeService.SetJobShiftTimeList(db, jobIds)
			if err != nil {
				return resp, err
			}
		}
		specialisationList = xtool.StringArrayDeduplication(specialisationList)
		specialisationMap, err := SelectionService.GetMapByCodes(db, specialisationList)
		if err != nil {
			return resp, err
		}
		invoiceAmounts, err := s.GetJobApplicationInvoiceAmount(db, JobApplicationInvoiceAmountReq{
			JobApplicationIds: jobApplicationIds,
		})
		if err != nil {
			return resp, err
		}
		invoiceAmountMap := make(map[uint64]JobApplicationInvoiceAmountResp)
		for _, item := range invoiceAmounts {
			invoiceAmountMap[item.JobApplicationId] = item
		}

		var acceptedApplicantMap map[uint64]string
		acceptedApplicantMap, err = JobService.GetJobAcceptedApplicantNames(db, jobIds)
		if err != nil {
			return resp, err
		}

		for i, job := range resp {
			if req.NeedShiftTime == "Y" {
				job.ShiftTime = shiftTimesMap[job.JobId]
			}

			if professionalNames, ok := acceptedApplicantMap[job.JobId]; ok {
				job.ProfessionalNames = professionalNames
			}
			if job.Specialisation != "" {
				job.SpecialisationName = specialisationMap[job.Specialisation]
			}

			// 工作預計收入金額
			job.ExpRecAmount = JobService.GetJobExpectedRevenueAmount(job.ShiftTime)

			if invoiceAmount, ok := invoiceAmountMap[job.JobApplicationId]; ok {
				// 工作應收金額
				job.InvoiceAmount = invoiceAmount.InvoiceAmount
				// 工作未收金額
				job.UnpaidAmount = invoiceAmount.UnpaidAmount
			}

			job.JobStatus, err = s.GetJobStatus(job)
			if err != nil {
				return resp, err
			}
			// 只有在Progress為空時，才額外計算進度
			if req.Progress == "" {
				job.Progress, err = s.GetProgress(job)
				if err != nil {
					return resp, err
				}
			}
			resp[i] = job
		}
	}

	return resp, nil
}

// 構建未開確認單的JobShift查詢
func (s *jobApplicationService) buildUninvoicedShiftQuery(db *gorm.DB, userId uint64) *gorm.DB {
	// 構建已提交確認單的 JobShift 查詢
	documentBuilder := db.Model(&model.Document{}).
		Table("document as d").
		Joins("JOIN document_item as di ON d.id = di.document_id").
		Select("di.job_shift_id").
		Where("d.progress <> ?", model.DocumentProgressCancel).
		Where("d.data_type <> ?", model.DocumentDataTypeSystemToFacility).
		Where("d.user_id = ?", userId).
		Group("di.job_shift_id")

	// 構建未開確認單的JobShift查詢
	nowTime := time.Now().UTC()

	return db.Model(&model.JobShift{}).
		Table("job_shift as js").
		Select("js.job_id").
		Joins("JOIN job as j ON js.job_id = j.id").
		Joins("JOIN job_application AS ja ON js.job_id = ja.job_id AND ja.user_id = ? AND ja.accept = ? AND ja.status = ?", userId, model.JobApplicationAcceptY, model.JobApplicationStatusAccept).
		Joins("LEFT JOIN (?) AS di ON js.id = di.job_shift_id", documentBuilder).
		Joins("LEFT JOIN job_shift_cancellation AS jsc ON js.id = jsc.job_shift_id AND jsc.job_application_id = ja.id").
		Where("di.job_shift_id IS NULL").  // 沒有提交確認單
		Where("jsc.id IS NULL").           // 沒有被取消 (Cancellation != Y)
		Where("js.end_time < ?", nowTime). // 工作已結束 (Status = Ready)
		Group("js.job_id")                 // 按工作ID分組，避免重複
}

// 獲取工作應收金額
type JobApplicationInvoiceAmountResp struct {
	JobApplicationId uint64          `json:"jobApplicationId"` // 工作申請Id
	InvoiceAmount    decimal.Decimal `json:"invoiceAmount"`    // 應收金額
	UnpaidAmount     decimal.Decimal `json:"unpaidAmount"`     // 未收金額
}

type JobApplicationInvoiceAmountReq struct {
	JobApplicationIds []uint64 `json:"jobApplicationIds" binding:"required"` // 工作申請Id
}

func (s *jobApplicationService) GetJobApplicationInvoiceAmount(db *gorm.DB, req JobApplicationInvoiceAmountReq) ([]JobApplicationInvoiceAmountResp, error) {
	resp := make([]JobApplicationInvoiceAmountResp, 0)
	if err := db.Table("job_application AS ja").
		Joins("JOIN document AS d ON ja.id = d.job_application_id AND d.category = ? AND d.progress = ? AND d.data_type <> ?", model.DocumentCategoryInvoice, model.DocumentProgressConfirm, model.DocumentDataTypeSystemToFacility).
		Select([]string{
			"ja.id AS job_application_id",
			"SUM(d.grand_total) AS invoice_amount",
			fmt.Sprintf("SUM(CASE WHEN d.payment_received = '%s' THEN d.grand_total ELSE 0 END) AS unpaid_amount", model.DocumentProgressPaymentReceivedN),
		}).
		Where("ja.id IN (?)", req.JobApplicationIds).
		Group("ja.id").
		Find(&resp).Error; err != nil {
		return resp, err
	}
	return resp, nil
}

// 獲取工作狀態
func (s *jobApplicationService) GetJobStatus(job MyJobListResp) (string, error) {
	var jobStatus string
	if job.JobCurrentStatus == model.JobStatusCancel {
		if job.Accept == model.JobApplicationAcceptY {
			jobStatus = "CONFIRMED"
		} else {
			jobStatus = "APPLIED_ONLY"
		}
		return jobStatus, nil
	}
	switch job.Status {
	case model.JobApplicationStatusApply:
		jobStatus = "PENDING"
	case model.JobApplicationStatusChatting, model.JobApplicationStatusInvite:
		jobStatus = "CHATTING"
	case model.JobApplicationStatusDecline:
		jobStatus = "DECLINED"
	case model.JobApplicationStatusApplicationCancel, model.JobApplicationStatusFacilityCancel, model.JobApplicationStatusProfessionalCancel:
		if job.Accept == model.JobApplicationAcceptY {
			jobStatus = "CONFIRMED"
		} else {
			jobStatus = "APPLIED_ONLY"
		}
	case model.JobApplicationStatusAccept:
		if job.InvoiceAmount.IsZero() {
			jobStatus = "NO_INVOICE" // 未開立發票
		} else if job.UnpaidAmount.IsZero() {
			jobStatus = "FULLY_PAID" // 所有發票都已收款
		} else if job.UnpaidAmount.Equal(job.InvoiceAmount) {
			jobStatus = "UNPAID" // 完全未支付
		} else if job.UnpaidAmount.GreaterThan(decimal.Zero) {
			jobStatus = "PARTIALLY_PAID" // 有未收款發票也有已收款發票
		}
	}
	return jobStatus, nil
}

// 獲取工作進度
func (s *jobApplicationService) GetProgress(job MyJobListResp) (string, error) {
	if job.JobCurrentStatus == model.JobStatusCancel {
		return JobApplicationProgressCancel, nil
	}
	// 解析時間
	beginTime, err := time.ParseInLocation(time.RFC3339, job.BeginTime, time.UTC)
	if err != nil {
		return "", err
	}
	endTime, err := time.ParseInLocation(time.RFC3339, job.EndTime, time.UTC)
	if err != nil {
		return "", err
	}

	// 獲取當前時間
	nowTime := time.Now().UTC()

	// 根據 req.Progress 篩選邏輯判斷進度
	switch job.Status {
	case model.JobApplicationStatusAccept:
		// Upcoming: 已接受且工作開始時間大於當前時間
		if beginTime.After(nowTime) {
			return JobApplicationProgressUpcoming, nil
		}
		// In Progress: 已接受且當前時間在工作開始和結束時間之間
		if beginTime.Before(nowTime) && endTime.After(nowTime) {
			return JobApplicationProgressInProgress, nil
		}
		// Completed: 已接受且工作結束時間小於當前時間
		if endTime.Before(nowTime) {
			return JobApplicationProgressComplete, nil
		}
	case model.JobApplicationStatusApply, model.JobApplicationStatusChatting, model.JobApplicationStatusInvite:
		// Applied: 申請中、聊天中或被邀請
		return JobApplicationProgressApplied, nil
	case model.JobApplicationStatusApplicationCancel, model.JobApplicationStatusFacilityCancel, model.JobApplicationStatusProfessionalCancel:
		// Cancelled: 機構已取消或專業人員已取消
		return JobApplicationProgressCancel, nil
	}

	// 如果沒有符合的條件，返回空字符串
	return "", nil
}

// endregion ---------------------------------------------------- 工作職位申請列表-專業人士 ----------------------------------------------------

// region ---------------------------------------------------- 獲取我的工作職位統計 ----------------------------------------------------

// 工作職位統計請求
type MyJobStatisticReq struct {
	ReqUserId uint64 `json:"-"` // 請求者Id
}

type MyJobStatisticResp struct {
	UpcomingCount   int `json:"upcomingCount"`   // 待開始
	InProgressCount int `json:"inProgressCount"` // 進行中
}

func (s *jobApplicationService) MyJobStatistic(db *gorm.DB, req MyJobStatisticReq) (MyJobStatisticResp, error) {
	var resp MyJobStatisticResp

	nowTime := time.Now().UTC()
	nowStr := nowTime.Format(xtool.DateTimeSecA1)

	builder := db.Table("job_application AS ja").
		Joins("JOIN job AS j ON ja.job_id = j.id").
		Where("ja.deleted <> ?", model.JobApplicationDeletedY).
		Where("ja.status = ? AND j.begin_time > ?", model.JobApplicationStatusAccept, nowStr).
		Where("ja.user_id = ?", req.ReqUserId)

	if err := builder.Pluck("COUNT(DISTINCT ja.id) AS num", &resp.UpcomingCount).Error; err != nil {
		return resp, err
	}

	builder = db.Table("job_application AS ja").
		Joins("JOIN job AS j ON ja.job_id = j.id").
		Select("COUNT(DISTINCT ja.id) as in_progress_count").
		Where("ja.deleted <> ?", model.JobApplicationDeletedY).
		Where("ja.status = ? AND j.begin_time <= ? AND j.end_time >= ?", model.JobApplicationStatusAccept, nowStr, nowStr).
		Where("ja.user_id = ?", req.ReqUserId)

	if err := builder.Pluck("in_progress_count", &resp.InProgressCount).Error; err != nil {
		return resp, err
	}

	return resp, nil
}

// endregion ---------------------------------------------------- 獲取我的工作職位統計 ----------------------------------------------------

// region ---------------------------------------------------- 撤回工作申請 ----------------------------------------------------

// 撤回我的工作申請請求
type MyJobWithdrawReq struct {
	JobApplicationId uint64 `json:"jobApplicationId" binding:"required"` // 工作申請Id
	ReqUserId        uint64 `json:"-"`                                   // 請求者Id
}

// 檢查工作申請是否可以撤回
func (s *jobApplicationService) CheckJobApplicationCanWithdraw(db *gorm.DB, jobApplicationId uint64, userId uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.job_application.cannot_withdraw.status",
		Other: "The job application cannot be withdrawn.",
	}
	notFoundMsg := i18n.Message{
		ID:    "checker.job_application.id.does_not_exist",
		Other: "No such application record, please try after reloading.",
	}
	// 查詢申請記錄
	var application model.JobApplication
	var err error
	if err = db.Where("id = ? AND user_id = ?", jobApplicationId, userId).First(&application).Error; xgorm.IsSqlErr(err) {
		if xgorm.IsNotFoundErr(err) {
			return false, msg, nil
		}
		return false, msg, err
	}
	if xgorm.IsNotFoundErr(err) {
		return false, notFoundMsg, nil
	}

	if application.Status == model.JobApplicationStatusInvite {
		// 機構正在"邀請你加入工作"，請先處理
		return false, i18n.Message{
			ID:    "checker.job_application.cannot_withdraw.invite",
			Other: "You have been invited to join a job by the facility. Please respond to the invitation first.",
		}, nil
	}

	// 只有狀態為已申請(APPLY)
	if application.Status != model.JobApplicationStatusApply && application.Status != model.JobApplicationStatusChatting && application.Status != model.JobApplicationStatusDecline {
		return false, msg, nil
	}

	return true, msg, nil
}

// 撤回工作申請
func (s *jobService) MyJobWithdraw(db *gorm.DB, req MyJobWithdrawReq) error {
	// 查詢申請記錄
	var application model.JobApplication
	// 鎖定行
	if err := db.Clauses(clause.Locking{Strength: "UPDATE"}).
		Where("id = ?", req.JobApplicationId).
		Where("user_id = ?", req.ReqUserId).
		Where("status in (?)", []string{model.JobApplicationStatusApply, model.JobApplicationStatusDecline}).
		First(&application).Error; err != nil {
		return err
	}

	// 更新申請狀態為已撤回
	if err := db.Model(&model.JobApplication{}).
		Where("id = ?", req.JobApplicationId).
		Updates(map[string]interface{}{
			"status":        model.JobApplicationStatusWithdraw,
			"withdraw_time": time.Now().UTC().Format(xtool.DateTimeSecA1),
		}).Error; err != nil {
		return err
	}

	// 將其它的撤銷記錄標記為已刪除
	if err := db.Model(&model.JobApplication{}).
		Where("job_id = ?", application.JobId).
		Where("user_id = ?", application.UserId).
		Where("id <> ?", application.Id).
		Where("status = ?", model.JobApplicationStatusWithdraw).
		Updates(map[string]interface{}{
			"deleted": model.JobApplicationDeletedY,
		}).Error; err != nil {
		return err
	}

	return nil
}

// endregion ---------------------------------------------------- 撤回工作申請 ----------------------------------------------------

// region ---------------------------------------------------- 取消我的工作申請 ----------------------------------------------------

type MyJobCancelReq struct {
	JobApplicationId uint64 `json:"jobApplicationId" binding:"required"` // 工作申請Id
	CancelReason     string `json:"cancelReason" binding:"required"`     // 取消原因
	ReqUserId        uint64 `json:"-"`                                   // 請求者Id
}

// 檢查工作申請是否可以取消
func (s *jobApplicationService) CheckJobApplicationCanCancel(db *gorm.DB, jobApplicationId uint64, userId uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.job_application.cannot_cancel",
		Other: "The job application cannot be cancelled.",
	}
	notFoundMsg := i18n.Message{
		ID:    "checker.job_application.id.does_not_exist",
		Other: "No such application record, please try after reloading.",
	}
	// 查詢申請記錄
	var application model.JobApplication
	var err error
	if err = db.Where("id = ? AND user_id = ?", jobApplicationId, userId).First(&application).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if xgorm.IsNotFoundErr(err) {
		return false, notFoundMsg, nil
	}
	// 只有狀態為已接受(ACCEPT)才可以取消
	if application.Status != model.JobApplicationStatusChatting && application.Status != model.JobApplicationStatusInvite && application.Status != model.JobApplicationStatusAccept {
		return false, msg, nil
	}
	return true, i18n.Message{}, nil
}

// 取消工作申請
func (s *jobApplicationService) MyJobCancel(db *gorm.DB, req MyJobCancelReq) error {
	// 查詢申請記錄
	var application model.JobApplication
	// 鎖定行
	if err := db.Clauses(clause.Locking{Strength: "UPDATE"}).
		Where("id = ?", req.JobApplicationId).
		Where("user_id = ?", req.ReqUserId).
		Where("status = ? OR status = ? OR status = ?", model.JobApplicationStatusChatting, model.JobApplicationStatusInvite, model.JobApplicationStatusAccept).
		First(&application).Error; err != nil {
		return err
	}
	updateMap := map[string]interface{}{
		"status":         model.JobApplicationStatusProfessionalCancel,
		"cancel_time":    time.Now().UTC().Format(xtool.DateTimeSecA1),
		"cancel_reason":  req.CancelReason,
		"cancel_user_id": req.ReqUserId,
	}

	if application.Status == model.JobApplicationStatusAccept {
		// 更新工作的 accepted count
		var job model.Job
		if err := db.Clauses(clause.Locking{Strength: "UPDATE"}).
			Where("id = ?", application.JobId).
			First(&job).Error; err != nil {
			return err
		}
		job.AcceptedCount = job.AcceptedCount - 1
		if err := db.Save(&job).Error; err != nil {
			return err
		}

		// 工作被Professional取消 - 通知Facility
		if err := SystemNotificationService.CreateFacilityJobCancelled(db, CreateFacilityJobCancelledReq{
			FacilityId:       application.FacilityId,
			JobId:            application.JobId,
			JobApplicationId: application.Id,
			CreatorUserId:    req.ReqUserId,
		}); err != nil {
			return err
		}
	}

	// 更新申請狀態為專業人員已取消
	if err := db.Model(&model.JobApplication{}).
		Where("id = ?", req.JobApplicationId).
		Updates(updateMap).Error; err != nil {
		return err
	}
	return nil
}

// endregion ---------------------------------------------------- 取消我的工作申請 ----------------------------------------------------

// region ---------------------------------------------------- 更新工作申請備註 ----------------------------------------------------
type MyJobUpdateCalendarNoteReq struct {
	JobApplicationId uint64 `json:"jobApplicationId" binding:"required"` // 工作申請Id
	Remark           string `json:"remark"`                              // 備註
	ReqUserId        uint64 `json:"-"`                                   // 請求者Id
}

// 檢查工作申請是否可以更新備註
func (s *jobApplicationService) CheckJobApplicationCanUpdateCalendarNote(db *gorm.DB, jobApplicationId uint64, userId uint64) (bool, i18n.Message, error) {
	notFoundMsg := i18n.Message{
		ID:    "checker.job_application.id.does_not_exist",
		Other: "No such application record, please try after reloading.",
	}
	// 查詢申請記錄
	var application model.JobApplication
	var err error
	if err = db.Where("id = ? AND user_id = ?", jobApplicationId, userId).First(&application).Error; xgorm.IsSqlErr(err) {
		return false, i18n.Message{}, err
	}
	if xgorm.IsNotFoundErr(err) {
		return false, notFoundMsg, nil
	}
	return true, i18n.Message{}, nil
}

// 更新工作申請備註
func (s *jobApplicationService) MyJobUpdateCalendarNote(db *gorm.DB, req MyJobUpdateCalendarNoteReq) error {
	// 更新備註
	if err := db.Model(&model.JobApplication{}).
		Where("user_id = ?", req.ReqUserId).
		Where("id = ?", req.JobApplicationId).
		Update("calendar_note", req.Remark).Error; err != nil {
		return err
	}
	return nil
}

// endregion ---------------------------------------------------- 更新工作申請備註 ----------------------------------------------------

// region ---------------------------------------------------- 獲取工作日曆日 ----------------------------------------------------

type JobCalendarDayReq struct {
	FacilityId         uint64 `form:"facilityId"`                                                        // 所屬機構Id
	PositionProfession string `form:"positionProfession"`                                                // 職位專業
	ServiceLocationId  uint64 `form:"serviceLocationId"`                                                 // 服務地點Id
	CalendarBeginTime  string `form:"calendarBeginTime" binding:"required,datetime=2006-01-02 15:04:05"` // 日曆開始時間
	CalendarEndTime    string `form:"calendarEndTime" binding:"required,datetime=2006-01-02 15:04:05"`   // 日曆結束時間
	Timezone           string `form:"timeZone" binding:"omitempty,timezone"`                             // 時區 如果沒有默認已經是機構的時區
	ReqUserId          uint64 `form:"-"`
}

type JobCalendarDayResp struct {
	JobId                  uint64                                  `json:"jobId"`                  // 工作Id
	PositionProfession     string                                  `json:"positionProfession"`     // 職位專業
	PositionProfessionName string                                  `json:"positionProfessionName"` // 職位專業名稱
	ServiceLocationId      uint64                                  `form:"serviceLocationId"`      // 服務地點Id
	ServiceLocationAddress string                                  `json:"serviceLocationAddress"` // 服務地點地址
	BeginTime              time.Time                               `json:"beginTime"`              // 工作開始時間
	EndTime                time.Time                               `json:"endTime"`                // 工作結束時間
	CalendarNote           string                                  `json:"calendarNote"`           // 日曆備註
	Timezone               string                                  `json:"timezone"`               // 時區
	MinHourlyRate          decimal.Decimal                         `json:"minHourlyRate"`          // 最低時薪
	MaxHourlyRate          decimal.Decimal                         `json:"maxHourlyRate"`          // 最高時薪
	NumberOfPeople         int32                                   `json:"numberOfPeople"`         // 需要人數
	AcceptedCount          int32                                   `json:"acceptedCount"`          // 已接受人數
	Applications           []CalendarJobApplicationResp            `json:"applications" gorm:"-"`  // 工作申請列表
	ShiftTime              []JobSearchForProfessionalShiftTimeResp `json:"shiftTime" gorm:"-"`     // 班次時間(日曆才返回)
}

type CalendarJobApplicationResp struct {
	JobApplicationId        uint64 `json:"jobApplicationId"`        // 工作申請Id
	JobId                   uint64 `json:"jobId"`                   // 工作Id
	UserId                  uint64 `json:"userId"`                  // 報名用戶Id
	ProfessionalId          uint64 `json:"professionalId"`          // 專業人員Id
	ProfessionalPhotoFileId uint64 `json:"professionalPhotoFileId"` // 專業人員照片文件Id
	ProfessionalName        string `json:"professionalName"`        // 專業人員名字
}

func (s *jobApplicationService) GetJobApplication(db *gorm.DB, jobIds []uint64) (map[uint64][]CalendarJobApplicationResp, error) {
	resp := make(map[uint64][]CalendarJobApplicationResp)
	data := make([]CalendarJobApplicationResp, 0)
	if err := db.Table("job_application AS ja").
		Joins("JOIN professional_file_relation AS pfr ON pfr.professional_id = ja.professional_id").
		Joins("JOIN professional_file AS pf ON pfr.professional_file_id = pf.id AND pf.user_id = ja.user_id AND pf.file_code = ?", model.ProfessionalFileCodePhoto).
		Select([]string{
			"ja.id AS job_application_id",
			"ja.job_id",
			"ja.user_id",
			"ja.professional_id",
			"pf.id AS professional_photo_file_id",
			"CONCAT(p.first_name, ' ', p.last_name) AS professional_name",
		}).Joins("JOIN professional p ON ja.professional_id = p.id").
		Where("ja.status = ?", model.JobApplicationStatusAccept).
		Where("ja.job_id IN (?)", jobIds).Order("ja.id").Find(&data).Error; err != nil {
		return nil, err
	}
	for _, item := range data {
		resp[item.JobId] = append(resp[item.JobId], item)
	}
	return resp, nil
}

func (s *jobApplicationService) JobCalendarDay(db *gorm.DB, req JobCalendarDayReq) ([]JobCalendarDayResp, error) {
	var resp []JobCalendarDayResp
	var err error

	builder := db.Table("job AS j").
		Select([]string{
			"j.id AS job_id",
			"j.position_profession",
			"j.service_location_id",
			"IF(sl.address_extra IS NOT NULL AND sl.address_extra != '', CONCAT(sl.address_extra, ' ', sl.address), sl.address) AS service_location_address",
			"j.begin_time",
			"j.end_time",
			"j.calendar_note",
			"sl.timezone",
			"j.min_hourly_rate",
			"j.max_hourly_rate",
			"j.number_of_people",
			"j.accepted_count",
		}).
		Joins("JOIN service_location sl ON j.service_location_id = sl.id").
		Where("j.facility_id = ?", req.FacilityId).Where("j.accepted_count > 0").
		Where("j.status IN (?)", []string{model.JobStatusPublish, model.JobStatusDisable, model.JobStatusComplete})

	err = FacilityUserDepartmentService.GetCanAccessDepartmentByUserId(db, req.ReqUserId, func(departments []model.Department) {
		builder = builder.Joins("JOIN job_department AS jd ON jd.job_id = j.id")
		builder = builder.Scopes(FacilityUserDepartmentService.FilterByCanAccessDepartment("jd.department_id", departments))
	})
	if err != nil {
		return nil, err
	}

	if req.CalendarBeginTime != "" || req.CalendarEndTime != "" {

		tz := time.UTC
		if req.Timezone != "" {
			tz, err = time.LoadLocation(req.Timezone)
			if err != nil {
				return nil, err
			}
		}
		builder = builder.Joins("JOIN job_shift AS js ON js.job_id = j.id")
		if req.CalendarEndTime != "" {
			if req.Timezone != "" {
				beginTime, err := time.ParseInLocation(xtool.DateTimeSecA1, req.CalendarBeginTime, tz)
				if err != nil {
					return nil, err
				}
				builder = builder.Where("js.begin_time < ?", beginTime.UTC())
			} else {
				builder = builder.Where(xgorm.ConvertTZSql("js.begin_time", "sl.timezone", "<"), req.CalendarEndTime)
			}
		}
		if req.CalendarBeginTime != "" {
			if req.Timezone != "" {
				endTime, err := time.ParseInLocation(xtool.DateTimeSecA1, req.CalendarEndTime, tz)
				if err != nil {
					return nil, err
				}
				builder = builder.Where("js.end_time > ?", endTime.UTC())
			} else {
				builder = builder.Where(xgorm.ConvertTZSql("js.end_time", "sl.timezone", ">"), req.CalendarBeginTime)
			}
		}
	}
	if req.ServiceLocationId > 0 {
		builder = builder.Where("j.service_location_id = ?", req.ServiceLocationId)
	}
	if req.PositionProfession != "" {
		builder = builder.Where("j.position_profession = ?", req.PositionProfession)
	}
	if err = builder.
		Group("j.id").
		Order("j.begin_time").
		Order("j.id").
		Find(&resp).Error; err != nil {
		return resp, err
	}

	var jobIds []uint64
	for _, item := range resp {
		jobIds = append(jobIds, item.JobId)
	}
	if len(resp) > 0 {
		var shiftTimesMap map[uint64][]JobSearchForProfessionalShiftTimeResp
		var jobApplications map[uint64][]CalendarJobApplicationResp
		var professionSectionMap map[string]string
		shiftTimesMap, err = JobShiftTimeService.SetJobShiftTimeList(db, jobIds)
		if err != nil {
			return resp, err
		}
		jobApplications, err = s.GetJobApplication(db, jobIds)
		if err != nil {
			return resp, err
		}
		professionSectionMap, err = SelectionService.GetCodeNameMap(db, []string{model.SelectionTypeProfessionalProfession})
		if err != nil {
			return resp, err
		}

		for i, job := range resp {
			var found bool
			resp[i].ShiftTime, found = shiftTimesMap[job.JobId]
			if !found {
				resp[i].ShiftTime = make([]JobSearchForProfessionalShiftTimeResp, 0)
			}

			resp[i].Applications, found = jobApplications[job.JobId]
			if !found {
				resp[i].Applications = make([]CalendarJobApplicationResp, 0)
			}

			resp[i].PositionProfessionName = professionSectionMap[job.PositionProfession]
		}
	}

	return resp, nil
}

// endregion ---------------------------------------------------- 獲取工作日曆日 ----------------------------------------------------

// region ---------------------------------------------------- 獲取首頁日曆日 ----------------------------------------------------

type JobHomeScheduleReq struct {
	FacilityId         uint64 `form:"facilityId"`                                                // 所屬機構Id
	PositionProfession string `form:"positionProfession"`                                        // 職位專業
	ServiceLocationId  uint64 `form:"serviceLocationId"`                                         // 服務地點Id
	BeginTime          string `form:"beginTime" binding:"required,datetime=2006-01-02 15:04:05"` // 開始時間
	EndTime            string `form:"endTime" binding:"required,datetime=2006-01-02 15:04:05"`   // 結束時間
	Timezone           string `form:"timeZone" binding:"required,timezone"`                      // 時區
	ReqUserId          uint64 `form:"-"`
}

type JobHomeScheduleResp struct {
	JobId                  uint64                                  `json:"jobId"`                  // 工作Id
	PositionProfession     string                                  `json:"positionProfession"`     // 職位專業
	PositionProfessionName string                                  `json:"positionProfessionName"` // 職位專業名稱
	ServiceLocationId      uint64                                  `form:"serviceLocationId"`      // 服務地點Id
	ServiceLocationAddress string                                  `json:"serviceLocationAddress"` // 服務地點地址
	BeginTime              time.Time                               `json:"beginTime"`              // 工作開始時間
	EndTime                time.Time                               `json:"endTime"`                // 工作結束時間
	CalendarNote           string                                  `json:"calendarNote"`           // 日曆備註
	Timezone               string                                  `json:"timezone"`               // 時區
	MinHourlyRate          decimal.Decimal                         `json:"minHourlyRate"`          // 最低時薪
	MaxHourlyRate          decimal.Decimal                         `json:"maxHourlyRate"`          // 最高時薪
	NumberOfPeople         int32                                   `json:"numberOfPeople"`         // 需要人數
	AcceptedCount          int32                                   `json:"acceptedCount"`          // 已接受人數
	Applications           []CalendarJobApplicationResp            `json:"applications" gorm:"-"`  // 工作申請列表
	ShiftTime              []JobSearchForProfessionalShiftTimeResp `json:"shiftTime" gorm:"-"`     // 班次時間(日曆才返回)
}

func (s *jobApplicationService) JobHomeSchedule(db *gorm.DB, req JobHomeScheduleReq) ([]JobHomeScheduleResp, error) {
	var resp []JobHomeScheduleResp
	var err error

	builder := db.Table("job AS j").
		Select([]string{
			"j.id AS job_id",
			"j.position_profession",
			"j.service_location_id",
			"IF(sl.address_extra IS NOT NULL AND sl.address_extra != '', CONCAT(sl.address_extra, ' ', sl.address), sl.address) AS service_location_address",
			"j.begin_time",
			"j.end_time",
			"j.calendar_note",
			"sl.timezone",
			"j.min_hourly_rate",
			"j.max_hourly_rate",
			"j.number_of_people",
			"j.accepted_count",
		}).
		Joins("JOIN service_location sl ON j.service_location_id = sl.id").
		Where("j.facility_id = ?", req.FacilityId).
		Where("j.accepted_count > 0").
		Where("j.status IN (?)", []string{model.JobStatusPublish})

	err = FacilityUserDepartmentService.GetCanAccessDepartmentByUserId(db, req.ReqUserId, func(departments []model.Department) {
		builder = builder.Joins("JOIN job_department AS jd ON jd.job_id = j.id")
		builder = builder.Scopes(FacilityUserDepartmentService.FilterByCanAccessDepartment("jd.department_id", departments))
	})
	if err != nil {
		return nil, err
	}

	tz, err := time.LoadLocation(req.Timezone)
	if err != nil {
		return nil, err
	}
	// 首頁必填
	builder = builder.Joins("JOIN job_shift AS js ON js.job_id = j.id")
	beginTime, err := time.ParseInLocation(xtool.DateTimeSecA1, req.BeginTime, tz)
	if err != nil {
		return nil, err
	}
	endTime, err := time.ParseInLocation(xtool.DateTimeSecA1, req.EndTime, tz)
	if err != nil {
		return nil, err
	}
	builder = builder.Where("js.end_time > ?", beginTime.UTC()).
		Where("js.begin_time < ?", endTime.UTC())

	if req.ServiceLocationId > 0 {
		builder = builder.Where("j.service_location_id = ?", req.ServiceLocationId)
	}
	if req.PositionProfession != "" {
		builder = builder.Where("j.position_profession = ?", req.PositionProfession)
	}
	if err = builder.
		Group("j.id").
		Order("j.begin_time").
		Order("j.id").
		Find(&resp).Error; err != nil {
		return resp, err
	}

	var jobIds []uint64
	for _, item := range resp {
		jobIds = append(jobIds, item.JobId)
	}
	if len(resp) > 0 {
		var shiftTimesMap map[uint64][]JobSearchForProfessionalShiftTimeResp
		var jobApplications map[uint64][]CalendarJobApplicationResp
		var professionSectionMap map[string]string
		shiftTimesMap, err = JobShiftTimeService.SetJobShiftTimeList(db, jobIds)
		if err != nil {
			return resp, err
		}
		jobApplications, err = s.GetJobApplication(db, jobIds)
		if err != nil {
			return resp, err
		}
		professionSectionMap, err = SelectionService.GetCodeNameMap(db, []string{model.SelectionTypeProfessionalProfession})
		if err != nil {
			return resp, err
		}

		for i, job := range resp {
			var found bool
			resp[i].ShiftTime, found = shiftTimesMap[job.JobId]
			if !found {
				resp[i].ShiftTime = make([]JobSearchForProfessionalShiftTimeResp, 0)
			} else {
				// 過濾班次時間，只保留在指定時間範圍內的記錄
				var filteredShiftTime []JobSearchForProfessionalShiftTimeResp
				for _, shift := range resp[i].ShiftTime {
					// 檢查班次時間是否符合條件：js.end_time > beginTime.UTC() AND js.begin_time < endTime.UTC()
					if shift.EndTime.After(beginTime.UTC()) && shift.BeginTime.Before(endTime.UTC()) {
						filteredShiftTime = append(filteredShiftTime, shift)
					}
				}
				resp[i].ShiftTime = filteredShiftTime
			}

			resp[i].Applications, found = jobApplications[job.JobId]
			if !found {
				resp[i].Applications = make([]CalendarJobApplicationResp, 0)
			}

			resp[i].PositionProfessionName = professionSectionMap[job.PositionProfession]
		}
	}

	return resp, nil
}

// endregion ---------------------------------------------------- 獲取首頁日曆日 ----------------------------------------------------

// region ---------------------------------------------------- 獲取專業人員首頁日程 ----------------------------------------------------

// 專業人員首頁日程請求
type JobHomeScheduleByProfessionalReq struct {
	BeginTime string `form:"beginTime" binding:"required,datetime=2006-01-02 15:04:05"` // 開始時間
	EndTime   string `form:"endTime" binding:"required,datetime=2006-01-02 15:04:05"`   // 結束時間
	Timezone  string `form:"timeZone" binding:"required,timezone"`                      // 時區
	ReqUserId uint64 `form:"-" json:"-"`                                                // 請求用戶ID
}

// 專業人員首頁日程響應
type JobHomeScheduleByProfessionalResp struct {
	FacilityId       uint64                                  `json:"facilityId"`       // 機構ID
	FacilityName     string                                  `json:"facilityName"`     // 機構名稱
	JobId            uint64                                  `json:"jobId"`            // 工作ID
	JobApplicationId uint64                                  `json:"jobApplicationId"` // 工作申請ID
	CalendarNote     string                                  `json:"calendarNote"`     // 日曆備註
	ShiftTime        []JobSearchForProfessionalShiftTimeResp `json:"shiftTime"`        // 班次時間
	Timezone         string                                  `json:"timezone"`         // 時區
}

// 班次及相關工作信息
type JobShiftWithJobInfo struct {
	// 工作信息
	FacilityId       uint64 `json:"facilityId"`
	FacilityName     string `json:"facilityName"`
	JobId            uint64 `json:"jobId"`
	JobApplicationId uint64 `json:"jobApplicationId"`
	CalendarNote     string `json:"calendarNote"`
	Timezone         string `json:"timezone"`
	// 班次信息
	JobShiftId       uint64          `json:"jobShiftId"`
	BeginTime        time.Time       `json:"beginTime"`
	EndTime          time.Time       `json:"endTime"`
	Duration         decimal.Decimal `json:"duration"`
	BreakDuration    decimal.Decimal `json:"breakDuration"`
	PayHours         decimal.Decimal `json:"payHours"`
	HourlyRate       decimal.Decimal `json:"hourlyRate"`
	AllowanceAmount  decimal.Decimal `json:"allowanceAmount"`
	BreakTimePayable string          `json:"breakTimePayable"`
}

// 獲取專業人員首頁日程
func (s *jobApplicationService) JobHomeScheduleByProfessional(db *gorm.DB, req JobHomeScheduleByProfessionalReq) ([]JobHomeScheduleByProfessionalResp, error) {
	var resp []JobHomeScheduleByProfessionalResp
	var err error

	// 解析時區和時間
	tz, err := time.LoadLocation(req.Timezone)
	if err != nil {
		return nil, err
	}

	beginTime, err := time.ParseInLocation(xtool.DateTimeSecA1, req.BeginTime, tz)
	if err != nil {
		return nil, err
	}

	endTime, err := time.ParseInLocation(xtool.DateTimeSecA1, req.EndTime, tz)
	if err != nil {
		return nil, err
	}

	var jobShiftResults []JobShiftWithJobInfo
	builder := db.Table("job_shift AS js").
		Select([]string{
			// 工作信息
			"j.facility_id",
			"fp.name AS facility_name",
			"ja.job_id",
			"ja.id AS job_application_id",
			"ja.calendar_note",
			"sl.timezone",
			// 班次信息
			"js.id AS job_shift_id",
			"js.begin_time",
			"js.end_time",
			"js.duration",
			"js.break_duration",
			"js.pay_hours",
			"js.hourly_rate",
			"js.allowance_amount",
			"js.break_time_payable",
		}).
		Joins("JOIN job AS j ON js.job_id = j.id").
		Joins("JOIN job_application AS ja ON ja.job_id = j.id").
		Joins("JOIN facility AS f ON j.facility_id = f.id").
		Joins("JOIN facility_profile AS fp ON f.id = fp.facility_id AND fp.data_type = ?", model.FacilityProfileDataTypeApproved).
		Joins("JOIN service_location AS sl ON j.service_location_id = sl.id").
		Where("ja.user_id = ?", req.ReqUserId).
		Where("ja.status = ?", model.JobApplicationStatusAccept).
		Where("ja.accept = ?", model.JobApplicationAcceptY).
		Where("ja.deleted <> ?", model.JobApplicationDeletedY).
		Where("j.status = ?", model.JobStatusPublish).
		// 直接在 JobShift 級別進行時間範圍篩選
		Where("js.end_time > ?", beginTime.UTC()).
		Where("js.begin_time < ?", endTime.UTC()).
		Order("js.begin_time ASC")

	if err = builder.Scan(&jobShiftResults).Error; err != nil {
		return nil, err
	}

	if len(jobShiftResults) == 0 {
		resp = make([]JobHomeScheduleByProfessionalResp, 0)
		return resp, nil
	}

	// 按工作分組並構建響應
	jobMap := make(map[uint64]*JobHomeScheduleByProfessionalResp)
	for _, item := range jobShiftResults {
		if _, exists := jobMap[item.JobId]; !exists {
			jobMap[item.JobId] = &JobHomeScheduleByProfessionalResp{
				FacilityId:       item.FacilityId,
				FacilityName:     item.FacilityName,
				JobId:            item.JobId,
				JobApplicationId: item.JobApplicationId,
				CalendarNote:     item.CalendarNote,
				Timezone:         item.Timezone,
				ShiftTime:        make([]JobSearchForProfessionalShiftTimeResp, 0),
			}
		}

		// 直接構建班次時間響應
		shiftTime := JobSearchForProfessionalShiftTimeResp{
			JobId:            item.JobId,
			JobShiftId:       item.JobShiftId,
			BeginTime:        item.BeginTime,
			EndTime:          item.EndTime,
			Duration:         item.Duration,
			BreakDuration:    item.BreakDuration,
			PayHours:         item.PayHours,
			HourlyRate:       item.HourlyRate,
			AllowanceAmount:  item.AllowanceAmount,
			BreakTimePayable: item.BreakTimePayable,
		}

		jobMap[item.JobId].ShiftTime = append(jobMap[item.JobId].ShiftTime, shiftTime)
	}

	// 轉換為響應數組
	resp = make([]JobHomeScheduleByProfessionalResp, 0, len(jobMap))
	for _, jobResp := range jobMap {
		resp = append(resp, *jobResp)
	}

	return resp, nil
}

// endregion ---------------------------------------------------- 獲取專業人員首頁日程 ----------------------------------------------------

// region ---------------------------------------------------- 獲取工作日曆月 ----------------------------------------------------

type JobCalendarMonthReq struct {
	FacilityId        uint64 `form:"facilityId" binding:"required"`                                     // 所屬機構Id
	CalendarBeginTime string `form:"calendarBeginTime" binding:"required,datetime=2006-01-02 15:04:05"` // 日曆開始時間
	CalendarEndTime   string `form:"calendarEndTime" binding:"required,datetime=2006-01-02 15:04:05"`   // 日曆結束時間
	ReqUserId         uint64 `form:"-"`
}

type JobCalendarMonthResp struct {
	CalendarDate string                     `json:"calendarDate"` // 日曆日期(YYYY-MM-DD)
	Jobs         []JobCalendarMonthDataResp `json:"jobs"`
}
type JobCalendarMonthDataResp struct {
	JobQty             int32  `json:"jobQty"`             // 工作數量
	PositionProfession string `json:"positionProfession"` // 職位專業
}

func (s *jobApplicationService) JobCalendarMonth(db *gorm.DB, req JobCalendarMonthReq) ([]JobCalendarMonthResp, error) {
	resp := make([]JobCalendarMonthResp, 0)
	jobs, err := s.JobCalendarDay(db, JobCalendarDayReq{
		FacilityId:        req.FacilityId,
		CalendarBeginTime: req.CalendarBeginTime,
		CalendarEndTime:   req.CalendarEndTime,
		ReqUserId:         req.ReqUserId,
	})
	if err != nil {
		return resp, err
	}
	mapByDate := make(map[string][]JobCalendarMonthDataResp)
	jobAlreadySet := make(map[string]map[uint64]bool)
	arrByDate := make([]string, 0)
	for _, job := range jobs {
		var tz *time.Location
		if tz, err = time.LoadLocation(job.Timezone); err != nil {
			return resp, err
		}
		for _, shiftTime := range job.ShiftTime {
			beginDate := shiftTime.BeginTime.In(tz).Format(xtool.DateDayA)

			if jobMap, exists := jobAlreadySet[beginDate]; exists {
				if alreadySet := jobMap[job.JobId]; alreadySet {
					// 相同的工作,在同一天,不同的時段只計算1
					continue
				}
			}
			if _, ok := mapByDate[beginDate]; !ok {
				arrByDate = append(arrByDate, beginDate)
				jobAlreadySet[beginDate] = make(map[uint64]bool)
				mapByDate[beginDate] = []JobCalendarMonthDataResp{}
			}
			var found bool
			for i, j := range mapByDate[beginDate] {
				if j.PositionProfession == job.PositionProfession {
					j.JobQty++
					mapByDate[beginDate][i] = j
					jobAlreadySet[beginDate][job.JobId] = true
					found = true
					break
				}
			}
			if !found {
				mapByDate[beginDate] = append(mapByDate[beginDate], JobCalendarMonthDataResp{
					JobQty:             1,
					PositionProfession: job.PositionProfession,
				})
				jobAlreadySet[beginDate][job.JobId] = true
			}
		}
	}
	for _, d := range arrByDate {
		resp = append(resp, JobCalendarMonthResp{
			CalendarDate: d,
			Jobs:         mapByDate[d],
		})
	}
	return resp, nil
}

// endregion ---------------------------------------------------- 獲取工作日曆月 ----------------------------------------------------

// region ---------------------------------------------------- 機構取消工作申請 ----------------------------------------------------

type FacilityJobApplicationCancelReq struct {
	JobId             uint64   `json:"jobId" binding:"required"`                   // 工作Id
	JobApplicationIds []uint64 `json:"jobApplicationIds" binding:"required,min=1"` // 工作申請Id
	FacilityId        uint64   `json:"facilityId" binding:"required"`
	CancelReason      string   `json:"cancelReason" binding:"required"` // 取消原因
	ReqUserId         uint64   `json:"-"`                               // 請求者Id
}

// 檢查工作申請是否可以取消
func (s *jobApplicationService) CheckJobApplicationCanCancelByFacility(db *gorm.DB, jobApplicationIds []uint64, jobId, facilityId uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.job_application.cannot_cancel",
		Other: "The job application cannot be cancelled.",
	}
	notFoundMsg := i18n.Message{
		ID:    "checker.job_application.id.does_not_exist",
		Other: "No such application record, please try after reloading.",
	}
	// 查詢申請記錄
	var applications []model.JobApplication
	jobApplicationIds = xtool.Uint64ArrayDeduplication(jobApplicationIds)
	var err error
	if err = db.Where("id IN (?)", jobApplicationIds).Where("job_id = ?", jobId).Where("facility_id = ?", facilityId).Find(&applications).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if len(jobApplicationIds) != len(applications) {
		return false, notFoundMsg, nil
	}
	// 只有狀態為已接受(ACCEPT)才可以取消
	for _, application := range applications {
		if application.Status != model.JobApplicationStatusAccept {
			return false, msg, nil
		}
	}
	return true, i18n.Message{}, nil
}

// 機構取消工作申請
func (s *jobApplicationService) JobApplicationCancelByFacility(db *gorm.DB, req FacilityJobApplicationCancelReq) error {
	// 查詢申請記錄
	var applications []model.JobApplication
	compensationApplications := make([]model.JobApplication, 0)
	// 鎖定行
	if err := db.Clauses(clause.Locking{Strength: "UPDATE"}).
		Where("id IN (?)", req.JobApplicationIds).
		Where("facility_id = ?", req.FacilityId).
		Where("status = ?", model.JobApplicationStatusAccept).
		Find(&applications).Error; err != nil {
		return err
	}
	var job model.Job
	if err := db.Clauses(clause.Locking{Strength: "UPDATE"}).
		Where("id = ?", req.JobId).
		First(&job).Error; err != nil {
		return err
	}
	professionalUserIds := make([]uint64, 0)
	applicationIds := make([]uint64, 0)
	for _, application := range applications {
		if application.Status == model.JobApplicationStatusAccept {
			// 更新工作的 accepted count
			job.AcceptedCount = job.AcceptedCount - 1
		}
		cancelStatus := model.JobApplicationStatusProfessionalCancel
		if application.Accept == model.JobApplicationAcceptY {
			cancelStatus = model.JobApplicationStatusFacilityCancel
			compensationApplications = append(compensationApplications, application)
		}
		// 更新申請狀態為專業人員已取消
		if err := db.Model(&model.JobApplication{}).
			Where("id = ?", application.Id).
			Updates(map[string]interface{}{
				"status":         cancelStatus,
				"cancel_time":    time.Now().UTC(),
				"cancel_reason":  req.CancelReason,
				"cancel_user_id": req.ReqUserId,
			}).Error; err != nil {
			return err
		}
		professionalUserIds = append(professionalUserIds, application.UserId)
		applicationIds = append(applicationIds, application.Id)
	}

	if err := db.Save(&job).Error; err != nil {
		return err
	}

	// 計算賠付
	if err := s.Compensation(db, req.ReqUserId, req.JobId, compensationApplications); err != nil {
		return err
	}
	// 工作被facility取消 - 通知已邀請的Professional
	if err := SystemNotificationService.CreateProfessionalJobCancelled(db, CreateProfessionalJobCancelledReq{
		ProfessionalUserIds: professionalUserIds,
		ApplicationIds:      applicationIds,
		FacilityId:          req.FacilityId,
		JobId:               req.JobId,
		CancellationReason:  req.CancelReason,
		CreatorUserId:       req.ReqUserId,
	}); err != nil {
		return err
	}
	return nil
}

// endregion ---------------------------------------------------- 機構取消工作申請 ----------------------------------------------------

// region ---------------------------------------------------- 計算賠付 ----------------------------------------------------

type GenerateCompensationReq struct {
	JobShiftId        uint64          `json:"jobShiftId"`
	JobApplicationId  uint64          `json:"jobApplicationId"`
	CompensationHours decimal.Decimal `json:"compensationHours"`
	Particular        string          `json:"particular"`
}

func (s *jobApplicationService) Compensation(db *gorm.DB, handleUser uint64, jobId uint64, applications []model.JobApplication) error {
	targetTime := time.Now().UTC()

	// 處理班次時間比較邏輯
	userMap := make(map[uint64]bool)
	shiftCancellations := make([]model.JobShiftCancellation, 0)
	for _, application := range applications {
		// 獲取 取消時所有影響的班次
		nextJobShifts, err := JobShiftTimeService.GetNextValidJobShiftsByJobId(db, jobId, application.Id, targetTime)
		if err != nil {
			return err
		}

		for i, shift := range nextJobShifts {
			if shift.BeginTime == nil {
				continue
			}

			if i == 0 {
				// 計算時間差（小時）
				timeDiff := shift.BeginTime.Sub(targetTime)
				hoursDiff := timeDiff.Hours()

				if timeDiff > 0 {
					// beginTime 大於 targetTime 班次未開始
					if hoursDiff > 0 && hoursDiff <= 1 {
						// 1小時內的處理邏輯 - 需要賠付
						compensationHours := decimal.NewFromInt(2)
						if shift.PayHours.LessThan(decimal.NewFromInt(2)) {
							compensationHours = shift.PayHours
						}
						if !userMap[application.UserId] {
							shiftCancellations = append(shiftCancellations, model.JobShiftCancellation{
								FacilityId:           application.FacilityId,
								JobShiftId:           shift.Id,
								JobApplicationId:     application.Id,
								ProfessionalId:       application.ProfessionalId,
								RequiresCompensation: "Y",
								CompensationHours:    compensationHours,
								CompensationAmount:   shift.HourlyRate.Mul(compensationHours),
								CompensationReason:   fmt.Sprintf("工作開始前1小時內取消，生成賠付數據"),
								CancelTime:           &targetTime,
								CancelUserId:         handleUser,
							})
							userMap[application.UserId] = true

							err = s.SendGenerateCompensationConfirmationNoteTask(GenerateCompensationReq{
								JobShiftId:        shift.Id,
								JobApplicationId:  application.Id,
								CompensationHours: compensationHours,
								Particular:        "Cancellation fee: Shift cancelled within 1 hour of start time",
							})
							if err != nil {
								return err
							}
						}
					} else {
						shiftCancellations = append(shiftCancellations, model.JobShiftCancellation{
							FacilityId:           application.FacilityId,
							JobShiftId:           shift.Id,
							JobApplicationId:     application.Id,
							ProfessionalId:       application.ProfessionalId,
							RequiresCompensation: "N",
							CancelTime:           &targetTime,
							CancelUserId:         handleUser,
						})
					}
				} else {
					// beginTime 少於 targetTime 班次已開始
					// 根據 shift time 的時數進行賠付
					if hoursDiff >= -4 {
						// 如果shift time時間大於4h,按照 4h賠付(payHours 不足時4h時,按照 payHours 付)
						compensationHours := decimal.NewFromInt(4)
						if shift.PayHours.LessThan(decimal.NewFromInt(4)) {
							compensationHours = shift.PayHours
						}
						if !userMap[application.UserId] {
							shiftCancellations = append(shiftCancellations, model.JobShiftCancellation{
								FacilityId:           application.FacilityId,
								JobShiftId:           shift.Id,
								JobApplicationId:     application.Id,
								ProfessionalId:       application.ProfessionalId,
								RequiresCompensation: "Y",
								CompensationHours:    compensationHours,
								CompensationAmount:   shift.HourlyRate.Mul(compensationHours),
								CompensationReason:   fmt.Sprintf("工作開始後取消，生成賠付數據(payHours:%sH)", compensationHours.String()),
								CancelTime:           &targetTime,
								CancelUserId:         handleUser,
							})
							userMap[application.UserId] = true

							err = s.SendGenerateCompensationConfirmationNoteTask(GenerateCompensationReq{
								JobShiftId:        shift.Id,
								JobApplicationId:  application.Id,
								CompensationHours: compensationHours,
								Particular:        "On-Arrival Cancellation",
							})
							if err != nil {
								return err
							}
						}
					}
				}
			} else {
				shiftCancellations = append(shiftCancellations, model.JobShiftCancellation{
					FacilityId:           application.FacilityId,
					JobShiftId:           shift.Id,
					JobApplicationId:     application.Id,
					ProfessionalId:       application.ProfessionalId,
					RequiresCompensation: "N",
					CancelTime:           &targetTime,
					CancelUserId:         handleUser,
				})
			}
		}
	}

	if len(shiftCancellations) > 0 {
		if err := db.Create(&shiftCancellations).Error; err != nil {
			return err
		}
	}
	return nil
}

// endregion ---------------------------------------------------- 計算賠付 ----------------------------------------------------

// region ---------------------------------------------------- 發送生成賠付賬單草稿任務 ----------------------------------------------------

// 發送生成賠付賬單草稿任務
func (s *jobApplicationService) SendGenerateCompensationConfirmationNoteTask(req GenerateCompensationReq) error {
	reqData, err := json.Marshal(req)
	if err != nil {
		return err
	}

	// 發送到隊列
	err = xamqp.SendTask(GenerateCompensationConfirmationNoteTask, xamqp.Task{
		MessageId: GenerateCompensationConfirmationNoteTask,
		TaskId:    GenerateCompensationConfirmationNoteTask + "_" + uuid.NewV4().String(),
		Data:      string(reqData),
	})
	if err != nil {
		return err
	}
	return nil
}

// endregion ---------------------------------------------------- 發送生成賠付賬單草稿任務 ----------------------------------------------------

// region ---------------------------------------------------- 接受邀請 - 專業人士 ----------------------------------------------------

// 接受邀請 - 專業人士
type JobAcceptInviteReq struct {
	FacilityId       uint64 `json:"facilityId" binding:"required"`       // 機構Id
	JobId            uint64 `json:"jobId" binding:"required"`            // 工作Id
	JobApplicationId uint64 `json:"jobApplicationId" binding:"required"` // 工作申請Id
	ReqUserId        uint64 `json:"-"`                                   // 請求者Id
}

// 接受邀請 - 專業人士
func (s *jobApplicationService) AcceptInvite(db *gorm.DB, req JobAcceptInviteReq) error {
	var err error
	// 處理接受邀請
	if err = s.HandleAcceptInvite(db, req); err != nil {
		return err
	}
	// 發送接受邀請消息 - WebSocket
	if err = s.ProfessionalHandleInviteWsMessage(db, req, model.WsMessageTypeJobAccept); err != nil {
		return err
	}

	return nil
}

// action: model.WsMessageTypeJobAccept, model.WsMessageTypeJobDecline
func (s *jobApplicationService) ProfessionalHandleInviteWsMessage(db *gorm.DB, req JobAcceptInviteReq, action string) error {
	var err error

	// 更新邀請消息處理結果
	var processResult string
	switch action {
	case model.WsMessageTypeJobAccept:
		processResult = model.WsMessageProcessResultProfessionalAccept
	case model.WsMessageTypeJobDecline:
		processResult = model.WsMessageProcessResultProfessionalDecline
	default:
		return errors.New("action is not valid")
	}

	// 找到機構發出邀請的消息
	var wsMessage model.WsMessage
	if err = db.
		Where("job_id = ?", req.JobId).
		Where("job_application_id = ?", req.JobApplicationId).
		Where("message_type = ?", model.WsMessageTypeJobInvitation).
		Where("sender_id = ?", req.FacilityId).
		Where("sender_type = ?", model.WsMessageSenderTypeFacility).
		Where("receiver_id = ?", req.ReqUserId).
		Where("receiver_type = ?", model.WsMessageReceiverTypeProfessional).
		Where("processed = ?", model.WsMessageProcessedN).
		First(&wsMessage).Error; xgorm.IsSqlErr(err) {
		return err
	}
	if xgorm.IsNotFoundErr(err) {
		return nil
	}

	wsMessage.ProcessResult = processResult
	wsMessage.Processed = model.WsMessageProcessedY
	if err = db.Model(&model.WsMessage{}).
		Where("id = ?", wsMessage.Id).
		Updates(map[string]interface{}{
			"process_result": wsMessage.ProcessResult,
			"processed":      model.WsMessageProcessedY,
		}).Error; err != nil {
		return err
	}

	// 發送接受邀請消息 - WebSocket
	sessionId, sessionUuid, err := WsMessageService.GetOrCreateSession(db, req.ReqUserId, model.WsMessageSenderTypeProfessional, req.FacilityId, model.WsMessageReceiverTypeFacility, false)
	if err != nil {
		return err
	}
	var session model.WsSession
	if err := db.Where("id = ?", sessionId).First(&session).Error; err != nil {
		return err
	}
	message := model.WsMessage{
		MessageUuid:      uuid.NewV4().String(),
		SessionId:        sessionId,
		SenderId:         req.ReqUserId,
		SenderType:       model.WsMessageSenderTypeProfessional,
		ReceiverId:       req.FacilityId,
		RelatedMessageId: wsMessage.Id,
		JobId:            req.JobId,
		JobApplicationId: req.JobApplicationId,
		ReceiverType:     model.WsMessageReceiverTypeFacility,
		MessageType:      action, // model.WsMessageTypeJobAccept, model.WsMessageTypeJobDecline
		Content:          "",
		Status:           model.WsMessageStatusUnread,
		Processed:        model.WsMessageProcessedN,
		ProcessResult:    "",
		ReadTime:         nil,
		CreateTime:       time.Now().UTC().Truncate(time.Second),
	}
	if err = db.Create(&message).Error; err != nil {
		return err
	}
	WsMessageService.SendMessageToUser(db, sessionUuid, "", "", model.WsMessageSenderTypeProfessional, req.FacilityId, message)

	// 更新會話最後一條消息
	if err = db.Model(&model.WsSession{}).
		Where("id = ?", sessionId).
		Updates(map[string]interface{}{
			"last_message_id":      message.Id,
			"last_message_time":    message.CreateTime,
			"last_message_type":    message.MessageType,
			"last_message_content": message.Content,
		}).Error; err != nil {
		return err
	}
	// 更新會話最後互動時間
	if err = db.Model(&model.WsSessionView{}).
		Where("session_id = ?", sessionId).
		Where("facility_id = ?", req.FacilityId).
		Update("last_interact_time", time.Now().UTC()).Error; err != nil {
		return err
	}
	return nil
}

// 接受邀請 - 專業人士
func (s *jobApplicationService) HandleAcceptInvite(db *gorm.DB, req JobAcceptInviteReq) error {
	if err := JobService.LockJob(db, req.JobId); err != nil {
		return err
	}

	updateMap := map[string]interface{}{
		"status":      model.JobApplicationStatusAccept,
		"accept":      model.JobApplicationAcceptY,
		"accept_time": time.Now().UTC().Truncate(time.Second),
	}
	// 將工作申請狀態設置為已接受
	if err := db.Model(&model.JobApplication{}).
		Where("id = ?", req.JobApplicationId).
		Updates(updateMap).Error; err != nil {
		return err
	}

	// 更新工作確認人數
	if err := db.Model(&model.Job{}).
		Where("id = ?", req.JobId).
		Update("accepted_count", gorm.Expr("accepted_count + 1")).Error; err != nil {
		return err
	}

	// 找到與此工作重疊的工作申請，並將其狀態設置為取消
	var jobShiftItems []model.JobShift
	if err := db.Model(&model.JobShift{}).
		Where("job_id = ?", req.JobId).
		Find(&jobShiftItems).Error; err != nil {
		return err
	}
	// 已經處理的工作id
	handledJobApplicationIds := make([]uint64, 0)
	for _, jobShiftItem := range jobShiftItems {
		var jobApplications []model.JobApplication
		applicationBuilder := db.Model(&model.JobApplication{}).
			Table("job_application AS ja").
			Joins("JOIN job_shift AS js ON js.job_id = ja.job_id").
			Where("ja.user_id = ?", req.ReqUserId).
			Where("ja.deleted <> ?", model.JobApplicationDeletedY).
			Where("ja.status IN (?)", []string{model.JobApplicationStatusApply, model.JobApplicationStatusChatting, model.JobApplicationStatusInvite}).
			Where("js.begin_time < ? AND js.end_time > ?", jobShiftItem.EndTime, jobShiftItem.BeginTime)
		if len(handledJobApplicationIds) > 0 {
			applicationBuilder = applicationBuilder.Where("ja.id NOT IN (?)", handledJobApplicationIds)
		}
		if err := applicationBuilder.Find(&jobApplications).Error; err != nil {
			return err
		}
		withdrawJobApplicationIds := make([]uint64, 0)
		for _, jobApplication := range jobApplications {
			if lo.Contains(handledJobApplicationIds, jobApplication.Id) {
				continue
			}
			withdrawJobApplicationIds = append(withdrawJobApplicationIds, jobApplication.Id)
		}
		if len(withdrawJobApplicationIds) > 0 {
			updateMap = map[string]interface{}{
				"status":        model.JobApplicationStatusWithdraw,
				"withdraw_time": time.Now().UTC().Truncate(time.Second),
			}
			if err := db.Model(&model.JobApplication{}).
				Where("id IN (?)", withdrawJobApplicationIds).
				Where("status IN (?)", []string{model.JobApplicationStatusApply, model.JobApplicationStatusChatting, model.JobApplicationStatusInvite}).
				Updates(updateMap).Error; err != nil {
				return err
			}
			handledJobApplicationIds = append(handledJobApplicationIds, withdrawJobApplicationIds...)
		}
	}
	// 獲取工作信息
	var job model.Job
	if err := db.Model(&model.Job{}).
		Where("id = ?", req.JobId).
		First(&job).Error; err != nil {
		return err
	}
	// 如果工作是預付款並且招滿人, 則生成預付發票
	if job.PaymentTerms == model.JobPaymentTermsPayUpfront && job.AcceptedCount == job.NumberOfPeople {
		createInvoiceReqData, err := json.Marshal(map[string]uint64{
			"jobId": req.JobId,
		})
		if err != nil {
			return err
		}
		// 發送到隊列
		err = xamqp.SendTask(CreateInvoiceDraftTask, xamqp.Task{
			MessageId: CreateInvoiceDraftTask,
			TaskId:    CreateInvoiceDraftTask + "_" + uuid.NewV4().String(),
			Data:      string(createInvoiceReqData),
		})
		if err != nil {
			return err
		}
	}

	// Professional接受工作邀請，該工作顯示在雙方的日程 - 通知Professional
	// 發送工作接受通知給專業人士
	if err := SystemNotificationService.CreateProfessionalJobAccepted(db, CreateProfessionalJobAcceptedReq{
		ProfessionalUserId: req.ReqUserId,
		JobId:              req.JobId,
		JobApplicationId:   req.JobApplicationId,
		FacilityId:         req.FacilityId,
		CreatorUserId:      req.ReqUserId,
		BeginTime:          *job.BeginTime,
		PositionProfession: job.PositionProfession,
	}); err != nil {
		return err
	}

	// Professional接受工作邀請，該工作顯示在雙方的日程 - 通知Facility
	// 發送工作接受通知給機構
	if err := SystemNotificationService.CreateFacilityJobAccepted(db, CreateFacilityJobAcceptedReq{
		FacilityId:         req.FacilityId,
		JobId:              req.JobId,
		JobApplicationId:   req.JobApplicationId,
		CreatorUserId:      req.ReqUserId,
		BeginTime:          *job.BeginTime,
		PositionProfession: job.PositionProfession,
	}); err != nil {
		return err
	}

	// Professional接受工作邀請後，如果該工作有培訓文件則通知professional要閱讀 - 通知Professional
	// 檢查該工作是否有培訓文件
	jobFiles, err := JobFileService.GetJobFilesByJobId(db, req.JobId)
	if err != nil {
		return err
	}

	// 如果有培訓文件，發送培訓文件通知
	if len(jobFiles) > 0 {
		if err = SystemNotificationService.CreateProfessionalJobOrientationDocument(db, CreateProfessionalJobOrientationDocumentReq{
			ProfessionalUserId: req.ReqUserId,
			JobId:              req.JobId,
			JobApplicationId:   req.JobApplicationId,
			ReqUserId:          req.ReqUserId,
		}); err != nil {
			return err
		}
	}

	return nil
}

// endregion ---------------------------------------------------- 接受邀請 - 專業人士 ----------------------------------------------------

// region ---------------------------------------------------- 拒絕邀請 - 專業人士 ----------------------------------------------------

type JobDeclineInviteReq struct {
	JobId            uint64 `json:"jobId" binding:"required"`            // 工作Id
	JobApplicationId uint64 `json:"jobApplicationId" binding:"required"` // 工作申請Id
	ReqUserId        uint64 `json:"-"`                                   // 請求者Id
}

// 拒絕邀請 - 專業人士
func (s *jobApplicationService) DeclineInvite(db *gorm.DB, req JobDeclineInviteReq) error {
	var err error
	// 處理拒絕邀請
	if err = s.HandleDeclineInvite(db, req); err != nil {
		return err
	}
	var job model.Job
	if err = db.Where("id = ?", req.JobId).First(&job).Error; err != nil {
		return err
	}

	// 發送拒絕邀請消息 - WebSocket
	handleWsReq := JobAcceptInviteReq{
		FacilityId:       job.FacilityId,
		JobId:            job.Id,
		JobApplicationId: req.JobApplicationId,
		ReqUserId:        req.ReqUserId,
	}
	if err = s.ProfessionalHandleInviteWsMessage(db, handleWsReq, model.WsMessageTypeJobDecline); err != nil {
		return err
	}

	return nil
}

func (s *jobApplicationService) HandleDeclineInvite(db *gorm.DB, req JobDeclineInviteReq) error {
	if err := JobService.LockJob(db, req.JobId); err != nil {
		return err
	}

	updateMap := map[string]interface{}{
		"status":       model.JobApplicationStatusDecline,
		"decline_time": time.Now().UTC().Truncate(time.Second),
	}
	// 將工作申請狀態設置為已拒絕
	if err := db.Model(&model.JobApplication{}).
		Where("id = ?", req.JobApplicationId).
		Where("user_id = ?", req.ReqUserId).
		Where("status = ?", model.JobApplicationStatusInvite).
		Updates(updateMap).Error; err != nil {
		return err
	}
	// Professional拒絕工作邀請 - 通知Facility
	if err := SystemNotificationService.CreateFacilityJobDecline(db, CreateFacilityJobDeclineReq{
		JobId:            req.JobId,
		JobApplicationId: req.JobApplicationId,
		CreatorUserId:    req.ReqUserId,
	}); err != nil {
		return err
	}

	return nil
}

// endregion ---------------------------------------------------- 拒絕邀請 - 專業人士 ----------------------------------------------------

// region ---------------------------------------------------- 機構查詢專業人士的申請列表 ----------------------------------------------------
type JobApplicationListByFacilitySessionReq struct {
	FacilityId         uint64 `form:"facilityId" binding:"required"`         // 機構Id
	ProfessionalUserId uint64 `form:"professionalUserId" binding:"required"` // 專業人士Id
	ReqUserId          uint64 `form:"reqUserId"`
}

// 機構查詢專業人士的申請列表
type JobApplicationListBySessionReq struct {
	FacilityId         uint64 // 機構Id
	ProfessionalUserId uint64 // 專業人士Id
	ReqUserId          uint64
}
type JobApplicationDetailBySessionResp struct {
	JobApplicationId       uint64                                  `json:"jobApplicationId"`                // 工作申請Id
	JobId                  uint64                                  `json:"jobId"`                           // 工作Id
	PositionProfession     string                                  `json:"positionProfession"`              // 職位專業
	PositionProfessionName string                                  `json:"positionProfessionName" gorm:"-"` // 職位專業名稱
	MinHourlyRate          decimal.Decimal                         `json:"minHourlyRate"`                   // 最低時薪
	MaxHourlyRate          decimal.Decimal                         `json:"maxHourlyRate"`                   // 最高時薪
	ServiceLocationAddress string                                  `json:"serviceLocationAddress"`          // 服務地點地址
	Timezone               string                                  `json:"timezone"`                        // 時區
	Status                 string                                  `json:"status"`                          // 工作申請狀態
	NumberOfPeople         int32                                   `json:"numberOfPeople"`                  // 需求人數
	AcceptedCount          int32                                   `json:"acceptedCount"`                   // 確認人數
	InvitedCount           int32                                   `json:"invitedCount"`                    // 邀請中的人數
	ShiftTime              []JobSearchForProfessionalShiftTimeResp `json:"shiftTime" gorm:"-"`              // 班次時間(日曆才返回)
}

func (s *jobApplicationService) JobApplicationListBySession(db *gorm.DB, req JobApplicationListBySessionReq) ([]JobApplicationDetailBySessionResp, error) {
	var err error
	resp := make([]JobApplicationDetailBySessionResp, 0)

	builder := db.Model(&model.JobApplication{}).
		Table("job_application AS ja").
		Joins("JOIN job AS j ON j.id = ja.job_id").
		Joins("JOIN service_location AS sl ON sl.id = j.service_location_id").
		Joins("LEFT JOIN job_application AS ia ON ia.job_id = j.id AND ia.status = ?", model.JobApplicationStatusInvite).
		Select([]string{
			"ja.id as job_application_id",
			"j.id as job_id",
			"j.position_profession as position_profession",
			"j.min_hourly_rate as min_hourly_rate",
			"j.max_hourly_rate as max_hourly_rate",
			"sl.address as service_location_address",
			"sl.timezone as timezone",
			"ja.status as status",
			"j.number_of_people as number_of_people",
			"j.accepted_count as accepted_count",
			"COUNT(ia.id) as invited_count",
		}).
		Where("ja.facility_id = ?", req.FacilityId).
		Where("ja.user_id = ?", req.ProfessionalUserId).
		Where("ja.status in (?)", []string{model.JobApplicationStatusApply, model.JobApplicationStatusChatting, model.JobApplicationStatusInvite, model.JobApplicationStatusAccept, model.JobApplicationStatusDecline}).
		Where("ja.deleted <> ?", model.JobApplicationDeletedY).
		Where("j.status = ?", model.JobStatusPublish)

	if req.ReqUserId > 0 {
		// 機構才會傳 ReqUserId
		err = FacilityUserDepartmentService.GetCanAccessDepartmentByUserId(db, req.ReqUserId, func(departments []model.Department) {
			builder = builder.Joins("JOIN job_department AS jd ON jd.job_id = j.id")
			builder = builder.Scopes(FacilityUserDepartmentService.FilterByCanAccessDepartment("jd.department_id", departments))
		})
		if err != nil {
			return nil, err
		}
	}

	nowTime := time.Now().UTC().Truncate(time.Second)
	builder = builder.Where("DATE_SUB(j.begin_time, INTERVAL 1 HOUR) > ?", nowTime). // 工作開始時間前1小時
												Where("j.publish_time < ?", nowTime) // 工作已經發佈

	if err = builder.Group("ja.id").Find(&resp).Error; err != nil {
		return nil, err
	}
	if len(resp) == 0 {
		resp = make([]JobApplicationDetailBySessionResp, 0)
		return resp, nil
	}
	jobIds := make([]uint64, 0)
	for _, v := range resp {
		jobIds = append(jobIds, v.JobId)
	}
	jobIds = xtool.Uint64ArrayDeduplication(jobIds)
	var shiftTimesMap map[uint64][]JobSearchForProfessionalShiftTimeResp
	shiftTimesMap, err = JobShiftTimeService.SetJobShiftTimeList(db, jobIds)
	if err != nil {
		return resp, err
	}
	var professionSectionMap map[string]string
	professionSectionMap, err = SelectionService.GetCodeNameMap(db, []string{model.SelectionTypeProfessionalProfession})
	if err != nil {
		return resp, err
	}
	for i, v := range resp {
		if shiftTimes, ok := shiftTimesMap[v.JobId]; ok {
			resp[i].ShiftTime = shiftTimes
		}
		if v, ok := professionSectionMap[v.PositionProfession]; ok {
			resp[i].PositionProfessionName = v
		}
	}

	return resp, nil
}

// endregion ---------------------------------------------------- 機構查詢專業人士的申請列表 ----------------------------------------------------

// region ---------------------------------------------------- 機構查詢專業人士的申請列表 ----------------------------------------------------

func (s *jobApplicationService) JobApplicationListByFacilitySession(db *gorm.DB, req JobApplicationListByFacilitySessionReq) ([]JobApplicationDetailBySessionResp, error) {
	var r JobApplicationListBySessionReq
	_ = copier.Copy(&r, req)
	return s.JobApplicationListBySession(db, r)
}

// endregion ---------------------------------------------------- 機構查詢專業人士的申請列表 ----------------------------------------------------

// region ---------------------------------------------------- 專業人士查詢在某個機構的已申請列表 ----------------------------------------------------

type JobApplicationListByProfessionalSessionReq struct {
	FacilityId         uint64 `form:"facilityId" binding:"required"` // 機構Id
	ProfessionalUserId uint64 `form:"-" json:"-"`                    // 專業人士Id
}

func (s *jobApplicationService) JobApplicationListByProfessionalSession(db *gorm.DB, req JobApplicationListByProfessionalSessionReq) ([]JobApplicationDetailBySessionResp, error) {
	var r JobApplicationListBySessionReq
	_ = copier.Copy(&r, req)
	return s.JobApplicationListBySession(db, r)
}

// 查詢在某個申請記錄的詳細資料
func (s *jobApplicationService) GetJobApplicationDetail(db *gorm.DB, jobApplicationId uint64) (JobApplicationDetailBySessionResp, error) {
	var err error
	var resp JobApplicationDetailBySessionResp
	builder := db.Model(&model.JobApplication{}).
		Table("job_application AS ja").
		Joins("JOIN job AS j ON j.id = ja.job_id").
		Joins("JOIN service_location AS sl ON sl.id = j.service_location_id").
		Joins("LEFT JOIN job_application AS ia ON ia.job_id = j.id AND ia.status = ?", model.JobApplicationStatusInvite).
		Select([]string{
			"ja.id as job_application_id",
			"j.id as job_id",
			"j.position_profession as position_profession",
			"j.min_hourly_rate as min_hourly_rate",
			"j.max_hourly_rate as max_hourly_rate",
			"sl.address as service_location_address",
			"sl.timezone as timezone",
			"ja.status as status",
			"j.number_of_people as number_of_people",
			"j.accepted_count as accepted_count",
			"COUNT(ia.id) as invited_count",
		}).
		Where("ja.id = ?", jobApplicationId)
	if err = builder.First(&resp).Error; err != nil {
		return resp, err
	}
	shiftTimesMap, err := JobShiftTimeService.SetJobShiftTimeList(db, []uint64{resp.JobId})
	if err != nil {
		return resp, err
	}
	resp.ShiftTime = shiftTimesMap[resp.JobId]

	var professionSectionMap map[string]string
	professionSectionMap, err = SelectionService.GetCodeNameMap(db, []string{model.SelectionTypeProfessionalProfession})
	if err != nil {
		return resp, err
	}
	if v, ok := professionSectionMap[resp.PositionProfession]; ok {
		resp.PositionProfessionName = v
	}

	return resp, nil
}

// endregion ---------------------------------------------------- 專業人士查詢在某個機構的已申請列表 ----------------------------------------------------

// region ---------------------------------------------------- 查詢在多個申請記錄的詳細資料 ----------------------------------------------------

// 查詢在多個申請記錄的詳細資料
func (s *jobApplicationService) GetJobApplicationDetailList(db *gorm.DB, jobApplicationIds []uint64) (map[uint64]JobApplicationDetailBySessionResp, error) {
	var err error
	resp := make(map[uint64]JobApplicationDetailBySessionResp)
	var result []JobApplicationDetailBySessionResp
	builder := db.Model(&model.JobApplication{}).
		Table("job_application AS ja").
		Joins("JOIN job AS j ON j.id = ja.job_id").
		Joins("JOIN service_location AS sl ON sl.id = j.service_location_id").
		Joins("LEFT JOIN job_application AS ia ON ia.job_id = j.id AND ia.status = ?", model.JobApplicationStatusInvite).
		Select([]string{
			"ja.id as job_application_id",
			"j.id as job_id",
			"j.position_profession",
			"j.min_hourly_rate",
			"j.max_hourly_rate",
			"sl.address as service_location_address",
			"sl.timezone",
			"ja.status",
			"j.number_of_people",
			"j.accepted_count",
			"COUNT(ia.id) as invited_count",
		}).
		Where("ja.id IN (?)", jobApplicationIds)
	if err = builder.Group("ja.id").Find(&result).Error; err != nil {
		return resp, err
	}
	if len(result) == 0 {
		return resp, nil
	}

	jobIds := make([]uint64, 0)
	for _, v := range result {
		jobIds = append(jobIds, v.JobId)
	}
	jobIds = xtool.Uint64ArrayDeduplication(jobIds)
	var shiftTimesMap map[uint64][]JobSearchForProfessionalShiftTimeResp
	shiftTimesMap, err = JobShiftTimeService.SetJobShiftTimeList(db, jobIds)
	if err != nil {
		return resp, err
	}
	var professionSectionMap map[string]string
	professionSectionMap, err = SelectionService.GetCodeNameMap(db, []string{model.SelectionTypeProfessionalProfession})
	if err != nil {
		return resp, err
	}
	for i, v := range result {
		if shiftTimes, ok := shiftTimesMap[v.JobId]; ok {
			result[i].ShiftTime = shiftTimes
		}
		if v, ok := professionSectionMap[v.PositionProfession]; ok {
			result[i].PositionProfessionName = v
		}
	}
	for _, v := range result {
		resp[v.JobApplicationId] = v
	}
	return resp, nil
}

// endregion ---------------------------------------------------- 查詢在多個申請記錄的詳細資料 ----------------------------------------------------

// region ---------------------------------------------------- 機構發起聊天 ----------------------------------------------------

type ChatByFacilityReq struct {
	FacilityId       uint64 `json:"facilityId" binding:"required"`       // 機構Id
	JobApplicationId uint64 `json:"jobApplicationId" binding:"required"` // 工作申請Id
	ReqUserId        uint64 `json:"-"`                                   // 請求者Id
}

func (s *jobApplicationService) ChatByFacility(db *gorm.DB, req ChatByFacilityReq) error {
	var jobApplication model.JobApplication
	if err := db.
		Where("id = ?", req.JobApplicationId).
		Where("facility_id = ?", req.FacilityId).
		First(&jobApplication).Error; err != nil {
		return err
	}
	if jobApplication.FacilityId != req.FacilityId {
		return errors.New("job application not found")
	}
	if jobApplication.Status == model.JobApplicationStatusApply {
		if err := db.
			Model(&jobApplication).
			Update("status", model.JobApplicationStatusChatting).Error; err != nil {
			return err
		}
	}

	// 創建聊天會話
	sessionId, _, err := WsMessageService.GetOrCreateSession(db, req.FacilityId, model.WsMessageSenderTypeFacility, jobApplication.UserId, model.WsMessageSenderTypeProfessional, true)
	if err != nil {
		return err
	}
	if err = db.Model(&model.WsSessionView{}).
		Where("session_id = ?", sessionId).
		Where("facility_id = ?", req.FacilityId).
		Update("last_interact_time", time.Now().UTC().Truncate(time.Second)).Error; err != nil {
		return err
	}

	return nil
}

// endregion ---------------------------------------------------- 機構發起聊天 ----------------------------------------------------

// region ---------------------------------------------------- 專業人士查詢在某個機構的已申請列表 ----------------------------------------------------
func (s *jobApplicationService) CheckSessionExistByProfessional(db *gorm.DB, jobApplicationId uint64, userId uint64, wsSessionId *uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "job_application.session_not_found",
		Other: "Session not found",
	}
	var jobApplication model.JobApplication
	if err := db.
		Where("id = ?", jobApplicationId).
		Where("user_id = ?", userId).
		First(&jobApplication).Error; err != nil {
		return false, i18n.Message{}, err
	}

	// 創建聊天會話
	sessionId, _, err := WsMessageService.GetOrCreateSession(db, jobApplication.FacilityId, model.WsMessageSenderTypeFacility, jobApplication.UserId, model.WsMessageSenderTypeProfessional, false)
	if err != nil {
		return false, i18n.Message{}, err
	}
	if sessionId == 0 {
		return false, msg, nil
	}
	*wsSessionId = sessionId
	return true, i18n.Message{}, nil
}

// endregion ---------------------------------------------------- 專業人士查詢在某個機構的已申請列表 ----------------------------------------------------

// region ---------------------------------------------------- 專業人士發起聊天 ----------------------------------------------------

type ChatByProfessionalReq struct {
	JobApplicationId uint64 `json:"jobApplicationId" binding:"required"` // 工作申請Id
	ReqUserId        uint64 `json:"-"`                                   // 專業人士用戶Id
	SessionId        uint64 `json:"-"`                                   // 聊天會話Id
}

func (s *jobApplicationService) ChatByProfessional(db *gorm.DB, req ChatByProfessionalReq) error {
	if err := db.Model(&model.WsSessionView{}).
		Where("session_id = ?", req.SessionId).
		Where("professional_user_id = ?", req.ReqUserId).
		Update("last_interact_time", time.Now().UTC().Truncate(time.Second)).Error; err != nil {
		return err
	}

	return nil
}

// endregion ---------------------------------------------------- 專業人士發起聊天 ----------------------------------------------------
