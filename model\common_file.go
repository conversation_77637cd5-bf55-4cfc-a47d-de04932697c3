package model

import "github.com/nicksnyder/go-i18n/v2/i18n"

const FileSizeLimitImageFile = 024 * 1024 * 10     // 10MB
const FileSizeLimitDocumentFile = 1024 * 1024 * 20 // 20MB

// 文檔類型的文件後綴(小寫）
var DocumentFileTypes = []string{
	".jpg",
	".jpeg",
	".png",
	".bmp",
	".pdf",
}

// 圖片類型的文件後綴(小寫）
var ImageFileTypes = []string{
	".jpg",
	".jpeg",
	".png",
	".bmp",
}

var MsgFileCodeNotExist = i18n.Message{
	ID:    "checker.file.code.does_not_exist",
	Other: "Some thing went wrong, please try again later.",
}

var MsgFileSizeTooLarge = i18n.Message{
	ID:    "checker.file.size.too_large",
	Other: "File size exceeds the limit of {{.MaxSize}} MB.",
}

var MsgFileTypeNotAllowed = i18n.Message{
	ID:    "checker.file.type.not_allowed",
	Other: "File type {{.FileType}} is not allowed. Allowed types: {{.AllowedTypes}}",
}
