package model

import (
	"github.com/Norray/xrocket/xmodel"
	"github.com/nicksnyder/go-i18n/v2/i18n"
)

const (
	// Part: Personal Information
	ProfessionalFileCodePhoto = "PROFESSIONAL_PHOTO" // 照片

	// Part: Work Preferences & Experience
	ProfessionalFileCodeQualificationCertificate      = "QUALIFICATION_CERTIFICATE"                       // 學歷資格證書
	ProfessionalFileCodeCurriculumVitae               = "PROFESSIONAL_CURRICULUM_VITAE"                   // 專業人士履歷
	ProfessionalFileCodePersonalCareWorkQualification = "PROFESSIONAL_PERSONAL_CARE_WORKER_QUALIFICATION" // 個人護理工作資格

	// Personal Care Worker Qualifications 個人護理工作者資格證書
	ProfessionalFileCodePersonalCareWorkerQualificationCertIIIIndividualSupport        = "PPCWQ_CERT_III_INDIVIDUAL_SUPPORT"  // Certificate III Individual Support
	ProfessionalFileCodePersonalCareWorkerQualificationCertificateIIIAGEDCARE          = "PPCWQ_CERT_III_AGED_CARE"           // Certificate III Aged Care
	ProfessionalFileCodePersonalCareWorkerQualificationCertificateIIIHomeCommunityCare = "PPCWQ_CERT_III_HOME_COMMUNITY_CARE" // Certificate III in Home and Community Care
	ProfessionalFileCodePersonalCareWorkerQualificationCertIIIDisabilities             = "PPCWQ_CERT_III_DISABILITIES"        // Certificate III in Disabilities
	ProfessionalFileCodePersonalCareWorkerQualificationCertIVAgeingSupport             = "PPCWQ_CERT_IV_AGEING_SUPPORT"       //Certificate IV in Ageing Support
	ProfessionalFileCodePersonalCareWorkerQualificationCertIVDisabilities              = "PPCWQ_CERT_IV_DISABILITY"           // Certificate IV in Disability
	ProfessionalFileCodePersonalCareWorkerQualificationCertificateIVHomeCommunityCare  = "PPCWQ_CERT_IV_HOME_COMMUNITY_CARE"  // Certificate IV in Home and Community Care
	ProfessionalFileCodePersonalCareWorkerQualificationBachelorNursing                 = "PPCWQ_BACHELOR_NURSING"             // Bachelor of Nursing
	ProfessionalFileCodePersonalCareWorkerQualificationDiplomaNursing                  = "PPCWQ_DIPLOMA_NURSING"              // Diploma of Nursing

	// Medical Practitioner Preferred Grade相關文件
	ProfessionalFileCodeRegistrarAccreditedEnrolment = "REGISTRAR_ACCREDITED_ENROLMENT" // Registrar (Accredited) 入學證明
	ProfessionalFileCodeFellowshipCertificate        = "FELLOWSHIP_CERTIFICATE"         // Fellowship證書
	ProfessionalFileCodeSpecialistQualification      = "SPECIALIST_QUALIFICATION"       // Specialist資格證明

	// Part: Registration & Certification
	ProfessionalFileCodeAhpraCertificate              = "AHPRA_CERT"         // AHPRA證書
	ProfessionalFileCodeAbn                           = "PROFESSIONAL_ABN"   // ABN
	ProfessionalFileCodeIndemnityInsuranceCertificate = "IND_INSURANCE_CERT" // 專業人士責任保險證明

	// Part: Proof of Identity & Records
	ProfessionalFileCodeIdCheck = "PROFESSIONAL_ID_CHECK" // 專業人士身份證明文件
	// Primary
	ProfessionalFileCodeAustralianPassport               = "AUSTRALIAN_PASSPORT"         // 澳洲護照
	ProfessionalFileCodeForeignPassport                  = "FOREIGN_PASSPORT"            // 外國護照
	ProfessionalFileCodeAustralianBirthCertificate       = "AUSTRALIAN_BIRTH_CERT"       // 澳洲出生證明
	ProfessionalFileCodeAustralianCitizenshipCertificate = "AUSTRALIAN_CITIZENSHIP_CERT" // 澳洲公民證
	// Secondary
	ProfessionalFileCodeCurrentAustraliaDriverLicence         = "CURRENT_AUSTRALIA_DRIVER_LICENCE"           // 澳洲駕照
	ProfessionalFileCodeAustralianPublicServiceEmployeeIDCard = "AUSTRALIAN_PUBLIC_SERVICE_EMPLOYEE_ID_CARD" // 澳洲公務員ID卡
	ProfessionalFileCodeOtherAustralianGovernmentIssueIDCard  = "OTHER_AUSTRALIAN_GOVERNMENT_ISSUE_ID_CARD"  // 其他澳洲政府發出的ID卡
	ProfessionalFileCodeTertiaryStudentIDCard                 = "TERTIARY_STUDENT_ID_CARD"                   // 大學生ID卡
	// Others
	ProfessionalFileCodeCreditDebitAtmCard                = "CREDIT_DEBIT_ATM_CARD"                // 信用卡/扣帳卡/ATM卡
	ProfessionalFileCodeMedicareCard                      = "MEDICARE_CARD"                        // 醫療卡
	ProfessionalFileCodeUtilityBillOrRateNotice           = "UTILITY_BILL_OR_RATE_NOTICE"          // 水電費單或收費通知
	ProfessionalFileCodeStatementFromFinancialInstitution = "STATEMENT_FROM_FINANCIAL_INSTITUTION" // 金融機構的結單
	ProfessionalFileCodeCentrelinkOrPensionCard           = "CENTRELINK_OR_PENSION_CARD"           // 澳洲国民福利署或養老金卡

	ProfessionalFileCodeVisa                                  = "VISA"                                       // 簽證
	ProfessionalFileCodeNationalCriminalCheck                 = "NATIONAL_CRIMINAL_CHECK"                    // 國家犯罪檢查
	ProfessionalFileCodeWorkingWithChildrenOrVulnerablePeople = "WORKING_WITH_CHILDREN_OR_VULNERABLE_PEOPLE" // 兒童/脆弱人群工作檢查
	ProfessionalFileCodeCurrentImmunisationRecords            = "CURRENT_IMMUNISATION_RECORDS"               // 現在的免疫記錄
	ProfessionalFileCodeCommonwealthStatutoryDeclaration      = "COMMONWEALTH_STATUTORY_DECLARATION"         // 聯邦法定聲明
	// Part: Additional Certification
	ProfessionalFileCodeAdditionalCertification = "ADDITIONAL_CERTIFICATION" // 附加證明
	ProfessionalFileCodeDisclosure              = "DISCLOSURE"               // 披露
	ProfessionalFileCodeSignedAgreement         = "SIGNED_AGREEMENT"         // 已簽署的協議

	ProfessionalFileCodeReferenceFormSignature = "REFERENCE_FORM_SIGNATURE" // 推薦人簽名

	ProfessionalFileCodeUpdatePrompt = "UPDATE_PROMPT" // 更新提示

	ProfessionalFileCodeSuperannuation = "SUPERANNUATION" // 养老金文件

	ProfessionalFileAiSuccessYes = "Y"
	ProfessionalFileAiSuccessNo  = "N"
)

// 專業人士文件
type ProfessionalFile struct {
	Id                 uint64 `json:"id" gorm:"primary_key"`
	UserId             uint64 `json:"userId" gorm:"index:userId_idx;not null"`               // 用戶ID
	FileCode           string `json:"fileCode" gorm:"type:varchar(255);not null"`            // 專業人士文件類型
	Mode               string `json:"mode" gorm:"type:varchar(255);not null"`                // 在OSS中的私有還是公開
	Bucket             string `json:"bucket" gorm:"type:varchar(255);not null"`              // Bucket
	Path               string `json:"path" gorm:"type:varchar(255);not null"`                // Bucket下的路徑
	Uuid               string `json:"uuid" gorm:"type:varchar(255);index:uuid_idx;not null"` // 唯一文件名
	OriginFileName     string `json:"originFileName" gorm:"type:varchar(255);not null"`      // 原文件名
	FileName           string `json:"fileName" gorm:"type:varchar(255);not null"`            // 唯一文件名
	FileType           string `json:"fileType" gorm:"type:varchar(255);not null"`            // 文件類型
	FileSize           uint32 `json:"fileSize" gorm:"not null"`                              // 文件大小
	ThumbnailPath      string `json:"thumbnailPath" gorm:"type:varchar(255);not null"`       // 縮略圖路徑
	ThumbnailFileSize  uint32 `json:"thumbnailFileSize" gorm:"not null"`                     // 縮略圖文件大小
	AiResultJson       string `json:"aiResultJson" gorm:"type:text;not null"`                // AI 提取信息結果（只有部分文件需要進行提取）
	AiModel            string `json:"aiModel" gorm:"type:varchar(255);not null"`             // AI 大模型名稱
	AiInputTokenUsage  int32  `json:"aiInputTokenUsage" gorm:"not null"`                     // AI 使用了多少個 Input token
	AiOutputTokenUsage int32  `json:"aiOutputUsageToken" gorm:"not null"`                    // AI 使用了多少個 Output token
	xmodel.Model
}

type ProfessionalFileAiResultCache struct {
	ExpiryDate  string `json:"expiryDate"`  // 到期日(YYYY-MM-DD)
	Number      string `json:"number"`      // 號碼
	Description string `json:"description"` // 描述
}

func (ProfessionalFile) TableName() string {
	return "professional_file"
}

func (ProfessionalFile) SwaggerDescription() string {
	return "專業人士文件"
}

var ProfessionalProfileFileNameMap = map[string]i18n.Message{
	ProfessionalFileCodePhoto:                         {ID: "professional_file.name.photo", Other: "Profile Photo"},
	ProfessionalFileCodeAhpraCertificate:              {ID: "professional_file.name.ahpra_certificate", Other: "AHPRA Registration"},
	ProfessionalFileCodeAbn:                           {ID: "professional_file.name.abn", Other: "ABN"},
	ProfessionalFileCodeIndemnityInsuranceCertificate: {ID: "professional_file.name.indemnity_insurance_certificate", Other: "Indemnity Insurance Certificate"},
	// Personal Care Worker Qualifications 個人護理工作者資格證書（按順序）
	ProfessionalFileCodePersonalCareWorkerQualificationCertIIIIndividualSupport:        {ID: "professional_file.name.personal_care_worker_qualification_cert_iii_individual_support", Other: "Certificate III Individual Support"},
	ProfessionalFileCodePersonalCareWorkerQualificationCertificateIIIAGEDCARE:          {ID: "professional_file.name.personal_care_worker_qualification_certificate_iii_aged_care", Other: "Certificate III Aged Care"},
	ProfessionalFileCodePersonalCareWorkerQualificationCertificateIIIHomeCommunityCare: {ID: "professional_file.name.personal_care_worker_qualification_certificate_iii_home_community_care", Other: "Certificate III in Home and Community Care"},
	ProfessionalFileCodePersonalCareWorkerQualificationCertIIIDisabilities:             {ID: "professional_file.name.personal_care_worker_qualification_cert_iii_disabilities", Other: "Certificate III in Disabilities"},
	ProfessionalFileCodePersonalCareWorkerQualificationCertIVAgeingSupport:             {ID: "professional_file.name.personal_care_worker_qualification_cert_iv_ageing_support", Other: "Certificate IV in Ageing Support"},
	ProfessionalFileCodePersonalCareWorkerQualificationCertIVDisabilities:              {ID: "professional_file.name.personal_care_worker_qualification_cert_iv_disability", Other: "Certificate IV in Disability"},
	ProfessionalFileCodePersonalCareWorkerQualificationCertificateIVHomeCommunityCare:  {ID: "professional_file.name.personal_care_worker_qualification_certificate_iv_home_community_care", Other: "Certificate IV in Home and Community Care"},
	ProfessionalFileCodePersonalCareWorkerQualificationBachelorNursing:                 {ID: "professional_file.name.personal_care_worker_qualification_bachelor_nursing", Other: "Bachelor of Nursing"},
	ProfessionalFileCodePersonalCareWorkerQualificationDiplomaNursing:                  {ID: "professional_file.name.personal_care_worker_qualification_diploma_nursing", Other: "Diploma of Nursing"},
	ProfessionalFileCodeAustralianPassport:                                             {ID: "professional_file.name.australian_passport", Other: "Australian passport (current or expired less than 2 years ago)"},
	ProfessionalFileCodeForeignPassport:                                                {ID: "professional_file.name.foreign_passport", Other: "Foreign passport"},
	ProfessionalFileCodeAustralianBirthCertificate:                                     {ID: "professional_file.name.australian_birth_certificate", Other: "Australian birth certificate"},
	ProfessionalFileCodeAustralianCitizenshipCertificate:                               {ID: "professional_file.name.australian_citizenship_certificate", Other: "Australian citizenship certificate"},
	ProfessionalFileCodeCurrentAustraliaDriverLicence:                                  {ID: "professional_file.name.current_australia_driver_licence", Other: "Current Australia driver licence"},
	ProfessionalFileCodeAustralianPublicServiceEmployeeIDCard:                          {ID: "professional_file.name.australian_public_service_employee_id_card", Other: "Australian Public Service employee ID card with photo"},
	ProfessionalFileCodeOtherAustralianGovernmentIssueIDCard:                           {ID: "professional_file.name.other_australian_government_issue_id_card", Other: "Other Australian government issue ID card with photo"},
	ProfessionalFileCodeTertiaryStudentIDCard:                                          {ID: "professional_file.name.tertiary_student_id_card", Other: "Tertiary student ID card with photo"},
	ProfessionalFileCodeCreditDebitAtmCard:                                             {ID: "professional_file.name.credit_debit_atm_card", Other: "Credit/Debit/ATM Card (maximum of one card from any one financial institution)"},
	ProfessionalFileCodeMedicareCard:                                                   {ID: "professional_file.name.medicare_card", Other: "Medicare card"},
	ProfessionalFileCodeUtilityBillOrRateNotice:                                        {ID: "professional_file.name.utility_bill_or_rate_notice", Other: "Utility bill or rate notice, for example water rates, council rates, electricity or gas; must be less than 12 months old"},
	ProfessionalFileCodeStatementFromFinancialInstitution:                              {ID: "professional_file.name.statement_from_financial_institution", Other: "Statement from a financial institution where you have held the account for at least one year"},
	ProfessionalFileCodeCentrelinkOrPensionCard:                                        {ID: "professional_file.name.centrelink_or_pension_card", Other: "Centrelink or pension card"},
	ProfessionalFileCodeVisa:                                                           {ID: "professional_file.name.visa", Other: "Visa"},
	ProfessionalFileCodeNationalCriminalCheck:                                          {ID: "professional_file.name.national_criminal_check", Other: "National Criminal Check"},
	ProfessionalFileCodeWorkingWithChildrenOrVulnerablePeople:                          {ID: "professional_file.name.working_with_children_or_vulnerable_people", Other: "Working With Children / Vulnerable People Check"},
	ProfessionalFileCodeCurrentImmunisationRecords:                                     {ID: "professional_file.name.current_immunisation_records", Other: "Current Immunisation Records"},
	ProfessionalFileCodeCommonwealthStatutoryDeclaration:                               {ID: "professional_file.name.commonwealth_statutory_declaration", Other: "Overseas Residency / Citizenship"},
	ProfessionalFileCodeAdditionalCertification:                                        {ID: "professional_file.name.additional_certification", Other: "Additional Certification"},
	ProfessionalFileCodeQualificationCertificate:                                       {ID: "professional_file.name.qualification_certificate", Other: "Graduating Institution"},
	ProfessionalFileCodeDisclosure:                                                     {ID: "professional_file.name.disclosure", Other: "Disclosures"},
	ProfessionalFileCodeSignedAgreement:                                                {ID: "professional_file.name.signed_agreement", Other: "Signed Agreement"},
	ProfessionalFileCodeRegistrarAccreditedEnrolment:                                   {ID: "professional_file.name.registrar_accredited_enrolment", Other: "Areas of Experience Registrar (Accredited) Enrolment"},
	ProfessionalFileCodeFellowshipCertificate:                                          {ID: "professional_file.name.fellowship_certificate", Other: "Areas of Experience Fellowship Certificate"},
	ProfessionalFileCodeSpecialistQualification:                                        {ID: "professional_file.name.specialist_qualification", Other: "Areas of Experience Specialist Qualification"},
	ProfessionalFileCodeCurriculumVitae:                                                {ID: "professional_file.name.curriculum_vitae", Other: "Experience CV"},
}

// 文件大小限制 (bytes)
func (ProfessionalFile) FileSizeLimitations() map[string]int64 {
	return map[string]int64{
		// Personal Information
		ProfessionalFileCodePhoto: FileSizeLimitImageFile,

		// Work Preferences & Experience
		ProfessionalFileCodeQualificationCertificate:      FileSizeLimitDocumentFile,
		ProfessionalFileCodeCurriculumVitae:               FileSizeLimitDocumentFile,
		ProfessionalFileCodePersonalCareWorkQualification: FileSizeLimitDocumentFile,

		// Personal Care Worker Qualifications
		ProfessionalFileCodePersonalCareWorkerQualificationCertIIIIndividualSupport:        FileSizeLimitDocumentFile,
		ProfessionalFileCodePersonalCareWorkerQualificationCertificateIIIAGEDCARE:          FileSizeLimitDocumentFile,
		ProfessionalFileCodePersonalCareWorkerQualificationCertificateIIIHomeCommunityCare: FileSizeLimitDocumentFile,
		ProfessionalFileCodePersonalCareWorkerQualificationCertIIIDisabilities:             FileSizeLimitDocumentFile,
		ProfessionalFileCodePersonalCareWorkerQualificationCertIVAgeingSupport:             FileSizeLimitDocumentFile,
		ProfessionalFileCodePersonalCareWorkerQualificationCertIVDisabilities:              FileSizeLimitDocumentFile,
		ProfessionalFileCodePersonalCareWorkerQualificationCertificateIVHomeCommunityCare:  FileSizeLimitDocumentFile,
		ProfessionalFileCodePersonalCareWorkerQualificationBachelorNursing:                 FileSizeLimitDocumentFile,
		ProfessionalFileCodePersonalCareWorkerQualificationDiplomaNursing:                  FileSizeLimitDocumentFile,

		// Medical Practitioner Preferred Grade
		ProfessionalFileCodeRegistrarAccreditedEnrolment: FileSizeLimitDocumentFile,
		ProfessionalFileCodeFellowshipCertificate:        FileSizeLimitDocumentFile,
		ProfessionalFileCodeSpecialistQualification:      FileSizeLimitDocumentFile,

		// Registration & Certification
		ProfessionalFileCodeAhpraCertificate:              FileSizeLimitDocumentFile,
		ProfessionalFileCodeAbn:                           FileSizeLimitDocumentFile,
		ProfessionalFileCodeIndemnityInsuranceCertificate: FileSizeLimitDocumentFile,

		// Proof of Identity & Records - Primary
		ProfessionalFileCodeIdCheck:                          FileSizeLimitDocumentFile,
		ProfessionalFileCodeAustralianPassport:               FileSizeLimitDocumentFile,
		ProfessionalFileCodeForeignPassport:                  FileSizeLimitDocumentFile,
		ProfessionalFileCodeAustralianBirthCertificate:       FileSizeLimitDocumentFile,
		ProfessionalFileCodeAustralianCitizenshipCertificate: FileSizeLimitDocumentFile,

		// Proof of Identity & Records - Secondary
		ProfessionalFileCodeCurrentAustraliaDriverLicence:         FileSizeLimitDocumentFile,
		ProfessionalFileCodeAustralianPublicServiceEmployeeIDCard: FileSizeLimitDocumentFile,
		ProfessionalFileCodeOtherAustralianGovernmentIssueIDCard:  FileSizeLimitDocumentFile,
		ProfessionalFileCodeTertiaryStudentIDCard:                 FileSizeLimitDocumentFile,

		// Proof of Identity & Records - Others
		ProfessionalFileCodeCreditDebitAtmCard:                FileSizeLimitDocumentFile,
		ProfessionalFileCodeMedicareCard:                      FileSizeLimitDocumentFile,
		ProfessionalFileCodeUtilityBillOrRateNotice:           FileSizeLimitDocumentFile,
		ProfessionalFileCodeStatementFromFinancialInstitution: FileSizeLimitDocumentFile,
		ProfessionalFileCodeCentrelinkOrPensionCard:           FileSizeLimitDocumentFile,

		// Other documents
		ProfessionalFileCodeVisa:                                  FileSizeLimitDocumentFile,
		ProfessionalFileCodeNationalCriminalCheck:                 FileSizeLimitDocumentFile,
		ProfessionalFileCodeWorkingWithChildrenOrVulnerablePeople: FileSizeLimitDocumentFile,
		ProfessionalFileCodeCurrentImmunisationRecords:            FileSizeLimitDocumentFile,
		ProfessionalFileCodeCommonwealthStatutoryDeclaration:      FileSizeLimitDocumentFile,

		// Additional Certification
		ProfessionalFileCodeAdditionalCertification: FileSizeLimitDocumentFile,
		ProfessionalFileCodeDisclosure:              FileSizeLimitDocumentFile,
		ProfessionalFileCodeSignedAgreement:         FileSizeLimitImageFile,
		ProfessionalFileCodeReferenceFormSignature:  FileSizeLimitDocumentFile,
		ProfessionalFileCodeUpdatePrompt:            FileSizeLimitDocumentFile,
		ProfessionalFileCodeSuperannuation:          FileSizeLimitDocumentFile,
	}
}

// 允許的文件類型
func (ProfessionalFile) AllowedFileTypes() map[string][]string {
	return map[string][]string{
		// Personal Information
		ProfessionalFileCodePhoto: ImageFileTypes,

		// Work Preferences & Experience
		ProfessionalFileCodeQualificationCertificate:      DocumentFileTypes,
		ProfessionalFileCodeCurriculumVitae:               DocumentFileTypes,
		ProfessionalFileCodePersonalCareWorkQualification: DocumentFileTypes,

		// Personal Care Worker Qualifications
		ProfessionalFileCodePersonalCareWorkerQualificationCertIIIIndividualSupport:        DocumentFileTypes,
		ProfessionalFileCodePersonalCareWorkerQualificationCertificateIIIAGEDCARE:          DocumentFileTypes,
		ProfessionalFileCodePersonalCareWorkerQualificationCertificateIIIHomeCommunityCare: DocumentFileTypes,
		ProfessionalFileCodePersonalCareWorkerQualificationCertIIIDisabilities:             DocumentFileTypes,
		ProfessionalFileCodePersonalCareWorkerQualificationCertIVAgeingSupport:             DocumentFileTypes,
		ProfessionalFileCodePersonalCareWorkerQualificationCertIVDisabilities:              DocumentFileTypes,
		ProfessionalFileCodePersonalCareWorkerQualificationCertificateIVHomeCommunityCare:  DocumentFileTypes,
		ProfessionalFileCodePersonalCareWorkerQualificationBachelorNursing:                 DocumentFileTypes,
		ProfessionalFileCodePersonalCareWorkerQualificationDiplomaNursing:                  DocumentFileTypes,

		// Medical Practitioner Preferred Grade
		ProfessionalFileCodeRegistrarAccreditedEnrolment: DocumentFileTypes,
		ProfessionalFileCodeFellowshipCertificate:        DocumentFileTypes,
		ProfessionalFileCodeSpecialistQualification:      DocumentFileTypes,

		// Registration & Certification
		ProfessionalFileCodeAhpraCertificate:              DocumentFileTypes,
		ProfessionalFileCodeAbn:                           DocumentFileTypes,
		ProfessionalFileCodeIndemnityInsuranceCertificate: DocumentFileTypes,

		// Proof of Identity & Records - Primary
		ProfessionalFileCodeIdCheck:                          DocumentFileTypes,
		ProfessionalFileCodeAustralianPassport:               DocumentFileTypes,
		ProfessionalFileCodeForeignPassport:                  DocumentFileTypes,
		ProfessionalFileCodeAustralianBirthCertificate:       DocumentFileTypes,
		ProfessionalFileCodeAustralianCitizenshipCertificate: DocumentFileTypes,

		// Proof of Identity & Records - Secondary
		ProfessionalFileCodeCurrentAustraliaDriverLicence:         DocumentFileTypes,
		ProfessionalFileCodeAustralianPublicServiceEmployeeIDCard: DocumentFileTypes,
		ProfessionalFileCodeOtherAustralianGovernmentIssueIDCard:  DocumentFileTypes,
		ProfessionalFileCodeTertiaryStudentIDCard:                 DocumentFileTypes,

		// Proof of Identity & Records - Others
		ProfessionalFileCodeCreditDebitAtmCard:                DocumentFileTypes,
		ProfessionalFileCodeMedicareCard:                      DocumentFileTypes,
		ProfessionalFileCodeUtilityBillOrRateNotice:           DocumentFileTypes,
		ProfessionalFileCodeStatementFromFinancialInstitution: DocumentFileTypes,
		ProfessionalFileCodeCentrelinkOrPensionCard:           DocumentFileTypes,

		// Other documents
		ProfessionalFileCodeVisa:                                  DocumentFileTypes,
		ProfessionalFileCodeNationalCriminalCheck:                 DocumentFileTypes,
		ProfessionalFileCodeWorkingWithChildrenOrVulnerablePeople: DocumentFileTypes,
		ProfessionalFileCodeCurrentImmunisationRecords:            DocumentFileTypes,
		ProfessionalFileCodeCommonwealthStatutoryDeclaration:      DocumentFileTypes,

		// Additional Certification
		ProfessionalFileCodeAdditionalCertification: DocumentFileTypes,
		ProfessionalFileCodeDisclosure:              DocumentFileTypes,
		ProfessionalFileCodeSignedAgreement:         DocumentFileTypes,
		ProfessionalFileCodeReferenceFormSignature:  ImageFileTypes,
		ProfessionalFileCodeUpdatePrompt:            DocumentFileTypes,
		ProfessionalFileCodeSuperannuation:          DocumentFileTypes,
	}
}
