package services

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"time"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/xconfig"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xhermes"
	"github.com/Norray/xrocket/xi18n"
	"github.com/Norray/xrocket/xmail"
	"github.com/Norray/xrocket/xredis"
	"github.com/Norray/xrocket/xs3"
	"github.com/Norray/xrocket/xtool"
	"github.com/matcornic/hermes/v2"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	uuid "github.com/satori/go.uuid"
	log "github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

const referenceFormVerifyKeyPrefix = "reference_form_verify:%s"

var ProfessionalReferenceFormService = new(professionalReferenceFormService)

type professionalReferenceFormService struct{}

type ProfessionalReferenceFormInquireReq struct {
	ProfessionalId uint64 `form:"professionalId" binding:"required"`
	FormUuid       string `form:"formUuid" binding:"required"`
	// 管理端不傳MatchUuid，跳過驗證
	MatchUuid string `form:"matchUuid" binding:"required"`
}

type ProfessionalReferenceFormSystemInquireReq struct {
	ProfessionalId uint64 `form:"professionalId" binding:"required"`
	FormUuid       string `form:"formUuid" binding:"required"`
}

type ProfessionalReferenceFormInquireResp struct {
	Valid  string                                  `json:"valid"` // Y N
	Form   *model.ProfessionalReferenceFormForUser `json:"form"`
	UserId uint64                                  `json:"-"` // Professional 的 UserId
}

func (s *professionalReferenceFormService) Inquire(db *gorm.DB, req ProfessionalReferenceFormInquireReq) (ProfessionalReferenceFormInquireResp, error) {
	var resp ProfessionalReferenceFormInquireResp

	var professional model.Professional
	if req.MatchUuid != "" {
		if err := db.Where("status = ?", model.ProfessionalStatusReviewing).Where("id = ?", req.ProfessionalId).First(&professional).Error; xgorm.IsSqlErr(err) {
			return resp, err
		}
	} else {
		if err := db.Where("id = ?", req.ProfessionalId).First(&professional).Error; xgorm.IsSqlErr(err) {
			return resp, err
		}
	}

	if professional.Id == 0 {
		resp.Valid = "N"
	} else {
		resp.UserId = professional.UserId
		profile, err := professional.UnmarshalProfile(professional.ProfileJson)
		if err != nil {
			return resp, err
		}
		for _, r := range profile.References {
			if r.FormUuid == req.FormUuid {
				var form model.ProfessionalReferenceForm
				err = json.Unmarshal([]byte(r.FormData), &form)
				if err != nil {
					return resp, err
				}
				var found bool
				if req.MatchUuid == "" {
					// 管理端不傳MatchUuid，跳過驗證
					found = true
				} else {
					for _, matchUuid := range form.MatchUuids {
						if matchUuid == req.MatchUuid && r.FormStatus == model.ProfessionalProfileReferencesFormStatusPending {
							found = true
						}
					}
				}
				if found {
					resp.Valid = "Y"
					formForUser := form.FormForUser()
					resp.Form = &formForUser
					if resp.Form.ElectronicSignature.ProfessionalFileUuid != "" {
						// 附帶文件數據
						var file model.ProfessionalFile
						if err = db.Where("uuid = ?", resp.Form.ElectronicSignature.ProfessionalFileUuid).Where("user_id = ?", professional.UserId).First(&file).Error; err != nil {
							return resp, err
						}
						var fileBytes []byte
						fileBytes, err = xs3.GetObject(file.Bucket, file.Path)
						if err != nil {
							return resp, err
						}
						base64Str := base64.StdEncoding.EncodeToString(fileBytes)
						resp.Form.ElectronicSignature.ProfessionalFileData = base64Str
					}
				} else {
					resp.Valid = "N"
				}
				break
			}
		}
	}

	return resp, nil
}

type ProfessionalReferenceFormVerifyReq struct {
	ProfessionalId   uint64 `json:"professionalId" binding:"required"`
	FormUuid         string `json:"formUuid" binding:"required"`
	VerificationCode string `json:"verificationCode" binding:"required"`
	DeviceUuid       string `json:"deviceUuid"  binding:"required"`
}

type ProfessionalReferenceFormVerifyResp struct {
	Status    string `json:"status"` // SUCCESS,NOT_FOUND,INCORRECT,LAST_INCORRECT
	MatchUuid string `json:"matchUuid"`
}

func (s *professionalReferenceFormService) Verify(db *gorm.DB, req ProfessionalReferenceFormVerifyReq) (ProfessionalReferenceFormVerifyResp, error) {
	var resp ProfessionalReferenceFormVerifyResp

	key := fmt.Sprintf(referenceFormVerifyKeyPrefix, req.DeviceUuid)
	var cache ProfessionalReferenceFormSendVerificationCache
	exist, err := xredis.GetStruct(db.Statement.Context, key, &cache)
	if err != nil {
		return ProfessionalReferenceFormVerifyResp{}, err
	}
	if !exist {
		resp.Status = "NOT_FOUND"
		return resp, nil
	}

	var professional model.Professional
	if err = db.Where("status = ?", model.ProfessionalStatusReviewing).Where("id = ?", req.ProfessionalId).First(&professional).Error; xgorm.IsSqlErr(err) {
		return resp, err
	}
	if professional.Id == 0 {
		resp.Status = "NOT_FOUND"
		return resp, nil
	} else {
		profile, err := professional.UnmarshalProfile(professional.ProfileJson)
		if err != nil {
			return resp, err
		}
		if req.VerificationCode != cache.Code {
			cache.TryQty++
			if cache.TryQty >= 5 {
				resp.Status = "LAST_INCORRECT"
				err = xredis.DeleteKey(db.Statement.Context, key)
				if err != nil {
					return ProfessionalReferenceFormVerifyResp{}, err
				}
				return resp, nil
			} else {
				err = xredis.UpdateStruct(db.Statement.Context, key, &cache, referenceFormExpiredTime)
				if err != nil {
					return ProfessionalReferenceFormVerifyResp{}, err
				}
				resp.Status = "INCORRECT"
				return resp, nil
			}
		} else {
			err = xredis.DeleteKey(db.Statement.Context, key)
			if err != nil {
				return ProfessionalReferenceFormVerifyResp{}, err
			}
			resp.Status = "SUCCESS"
		}
		for i, r := range profile.References {
			if r.FormUuid == req.FormUuid {
				var form model.ProfessionalReferenceForm
				err = json.Unmarshal([]byte(r.FormData), &form)
				if err != nil {
					return resp, err
				}
				newMatchUuid := uuid.NewV4().String()
				resp.MatchUuid = newMatchUuid
				form.MatchUuids = append(form.MatchUuids, newMatchUuid)
				var formJson []byte
				formJson, err = json.Marshal(form)
				if err != nil {
					return resp, err
				}
				profile.References[i].FormData = string(formJson)
				break
			}
		}
		err = professional.MarshalProfile(profile)
		if err != nil {
			return ProfessionalReferenceFormVerifyResp{}, err
		}
		if err = db.Save(&professional).Error; err != nil {
			return ProfessionalReferenceFormVerifyResp{}, err
		}
	}

	return resp, nil
}

type ProfessionalReferenceFormSendVerificationCodeReq struct {
	ProfessionalId uint64 `json:"professionalId" binding:"required"`
	FormUuid       string `json:"formUuid" binding:"required"`
}

type ProfessionalReferenceFormSendVerificationCodeResp struct {
	Valid      string `json:"valid"` // Y N
	DeviceUuid string `json:"deviceUuid"`
	Email      string `json:"email"` // 被 * 掉的電郵
}

type ProfessionalReferenceFormSendVerificationCache struct {
	ProfessionalId uint64 `json:"professionalId"`
	FormUuid       string `json:"formUuid"`
	Code           string `json:"code"`
	TryQty         int32  `json:"tryQty"` // 大於5次，刪除記錄，重新發送
}

// SendVerificationCode 發送驗證碼
func (s *professionalReferenceFormService) SendVerificationCode(db *gorm.DB, lang string, req ProfessionalReferenceFormSendVerificationCodeReq) (ProfessionalReferenceFormSendVerificationCodeResp, error) {
	var resp ProfessionalReferenceFormSendVerificationCodeResp

	var professional model.Professional
	var reference model.ProfessionalReference
	if err := db.Where("status = ?", model.ProfessionalStatusReviewing).Where("id = ?", req.ProfessionalId).First(&professional).Error; xgorm.IsSqlErr(err) {
		return resp, err
	}
	if professional.Id == 0 {
		resp.Valid = "N"
	} else {
		profile, err := professional.UnmarshalProfile(professional.ProfileJson)
		if err != nil {
			return resp, err
		}
		for _, r := range profile.References {
			if r.FormUuid == req.FormUuid {
				reference = r
				break
			}
		}
	}
	if reference.FormUuid == "" {
		resp.Valid = "N"
		return resp, nil
	}
	code, err := xtool.GenRandIntWithZeroDigit(999999, 6)
	if err != nil {
		return resp, err
	}

	deviceKey := uuid.NewV4().String()
	key := fmt.Sprintf(referenceFormVerifyKeyPrefix, deviceKey)
	err = xredis.SetStruct(db.Statement.Context, key, &ProfessionalReferenceFormSendVerificationCache{
		ProfessionalId: req.ProfessionalId,
		FormUuid:       req.FormUuid,
		Code:           code,
		TryQty:         0,
	}, referenceFormExpiredTime)
	if err != nil {
		return resp, err
	}
	err = s.SendVerificationCodeMail(lang, code, professional, reference)
	if err != nil {
		return resp, err
	}

	resp.Valid = "Y"
	resp.Email = UserDeviceService.starTheEmail(reference.ReferenceEmail)
	resp.DeviceUuid = deviceKey

	return resp, nil
}

func (s *professionalReferenceFormService) SendNotificationMailToAll(db *gorm.DB, lang string, professionalId uint64) error {
	var professional model.Professional
	if err := db.Where("id = ?", professionalId).First(&professional).Error; err != nil {
		return err
	}
	profile, err := professional.UnmarshalProfile(professional.ProfileJson)
	if err != nil {
		return err
	}
	go func() {
		for _, reference := range profile.References {
			err = s.SendNotificationMail(db, lang, professional, reference)
			if err != nil {
				log.Errorf("can not send reference form to %s", reference.ReferenceEmail)
			}
		}
	}()
	return nil
}

func (s *professionalReferenceFormService) SendNotificationMail(db *gorm.DB, lang string, professional model.Professional, reference model.ProfessionalReference) error {
	professionNameMap, err := SelectionService.GetCodeNameMap(db, []string{model.SelectionTypeProfessionalProfession})
	if err != nil {
		return nil
	}

	i18nMap := map[string]string{
		"ProfessionalName": professional.Name(),
		"ProfessionName":   professionNameMap[professional.Profession],
		"AOrAn":            model.ProfessionalReferenceForm{}.AOrAn(professionNameMap[professional.Profession]),
	}

	emailGreeting := i18n.Message{
		ID:    "reference_form.email.greeting",
		Other: "Hi",
	}
	emailSignature := i18n.Message{
		ID:    "reference_form.email.signature",
		Other: "Thank you",
	}
	emailIntro := i18n.Message{
		ID:    "reference_form.email.intro",
		Other: "{{.ProfessionalName}} has registered as {{.AOrAn}} {{.ProfessionName}} on the Medic Crew platform and nominated you as a referee.\n",
	}
	emailIntro1 := i18n.Message{
		ID:    "reference_form.email.intro1",
		Other: "{{.ProfessionalName}} is seeking to provide the following services:\n",
	}
	emailIntro2 := i18n.Message{
		ID:    "reference_form.email.intro2",
		Other: "Medic Crew helps connect healthcare providers with facilities seeking medical services, ensuring that practitioners with the right skills and interests are matched with facilities' needs. For More, visit www.mediccrew.com.au\n",
	}
	emailIntro3 := i18n.Message{
		ID:    "reference_form.email.intro3",
		Other: "Thank you for taking the time to complete this request. Your timely response is appreciated as it will help us speed up the process for {{.ProfessionalName}}. You can do this using your mobile device and it should take no longer than a few minutes.\n",
	}
	emailInstructions1 := i18n.Message{
		ID:    "reference_form.email.instructions",
		Other: "To complete your reference for {{.ProfessionalName}}",
	}
	emailButtonText1 := i18n.Message{
		ID:    "reference_form.email.button1.text",
		Other: "Complete reference",
	}
	// todo 下一期要用到
	//emailInstructions2 := i18n.Message{
	//	ID:    "reference_form.email.opt_out",
	//	Other: "Alternatively, if this email was sent to you by accident or you don't want to complete the reference, please opt out.",
	//}
	//emailButtonText2 := i18n.Message{
	//	ID:    "reference_form.email.button2.text",
	//	Other: "Opt out",
	//}
	emailSubject := i18n.Message{
		ID:    "reference_form.email.subject",
		Other: "Kindly complete a reference for {{.ProfessionalName}}",
	}

	var formUrl string
	formUrl, err = CommonSettingService.GetSettingValueByCode(db, model.CommonSettingCodeProfessionalReferenceFormUrl)
	if err != nil {
		return err
	}
	var form model.ProfessionalReferenceForm
	err = json.Unmarshal([]byte(reference.FormData), &form)
	if err != nil {
		return err
	}
	var matchUuid string
	if len(form.MatchUuids) > 0 {
		matchUuid = form.MatchUuids[0]
	}

	// 構建電郵內容
	body := hermes.Body{
		Greeting:  xi18n.LocalizeWithLangAndTemplateData(lang, &emailGreeting, i18nMap),
		Name:      reference.Name(),
		Signature: xi18n.LocalizeWithLangAndTemplateData(lang, &emailSignature, i18nMap),
		Intros: []string{
			xi18n.LocalizeWithLangAndTemplateData(lang, &emailIntro, i18nMap),
			xi18n.LocalizeWithLangAndTemplateData(lang, &emailIntro1, i18nMap),
			professionNameMap[professional.Profession] + "\n",
			xi18n.LocalizeWithLangAndTemplateData(lang, &emailIntro2, i18nMap),
			xi18n.LocalizeWithLangAndTemplateData(lang, &emailIntro3, i18nMap),
		},
		Actions: []hermes.Action{
			{
				Instructions: xi18n.LocalizeWithLangAndTemplateData(lang, &emailInstructions1, i18nMap),
				Button: hermes.Button{
					Color: MedicCrewButtonColor,
					Text:  xi18n.LocalizeWithLangAndTemplateData(lang, &emailButtonText1, i18nMap),
					Link:  fmt.Sprintf("%s?formUuid=%s&professionalId=%d&matchUuid=%s", formUrl, reference.FormUuid, professional.Id, matchUuid),
				},
			},
			// todo 下一期要用到
			//{
			//	Instructions: xi18n.LocalizeWithLangAndTemplateData(lang, &emailInstructions2, i18nMap),
			//	Button: hermes.Button{
			//		TextColor: MedicCrewButtonColor,
			//		Text:      xi18n.LocalizeWithLangAndTemplateData(lang, &emailButtonText2, i18nMap),
			//		Color:     "#E8F7F4",
			//		Link:      "",
			//	},
			//},
		},
	}
	content, err := xhermes.GenerateHTML(HermesDefaultProduct(medicCrewLogoUrl), body)
	if err != nil {
		return err
	}

	err = xmail.SendMail(reference.ReferenceEmail, fmt.Sprintf("[%s] %s", xconfig.MailConf.Name, xi18n.LocalizeWithLangAndTemplateData(lang, &emailSubject, i18nMap)), "text/html", content)
	if err != nil {
		return err
	}

	return nil
}

func (s *professionalReferenceFormService) SendVerificationCodeMail(lang, code string, professional model.Professional, reference model.ProfessionalReference) error {
	i18nMap := map[string]string{
		"ProfessionalName": professional.Name(),
	}
	emailGreeting := i18n.Message{
		ID:    "reference_form.email_verify.greeting",
		Other: "Hi",
	}
	emailSignature := i18n.Message{
		ID:    "reference_form.email_verify.signature",
		Other: "Thank you",
	}
	emailIntro := i18n.Message{
		ID:    "reference_form.email_verify.intro",
		Other: "You have been invited to provide a reference for {{.ProfessionalName}} on Medic Crew.",
	}
	emailIntro1 := i18n.Message{
		ID:    "reference_form.email_verify.intro1",
		Other: "To confirm your identity, please use the verification code below:",
	}
	emailIntro2 := i18n.Message{
		ID:    "reference_form.email_verify.intro2",
		Other: "Please enter this code on the verification page to proceed.",
	}
	emailSubject := i18n.Message{
		ID:    "reference_form.email_verify.subject",
		Other: "Referee Identity Verification",
	}
	// 構建電郵內容
	body := hermes.Body{
		Greeting:  xi18n.LocalizeWithLangAndTemplateData(lang, &emailGreeting, i18nMap),
		Name:      reference.Name(),
		Signature: xi18n.LocalizeWithLangAndTemplateData(lang, &emailSignature, i18nMap),
		Intros: []string{
			xi18n.LocalizeWithLangAndTemplateData(lang, &emailIntro, i18nMap),
		},
		Actions: []hermes.Action{
			{
				Instructions: xi18n.LocalizeWithLangAndTemplateData(lang, &emailIntro1, i18nMap),
				InviteCode:   code,
			},
			{
				Instructions: xi18n.LocalizeWithLangAndTemplateData(lang, &emailIntro2, i18nMap),
			},
		},
	}
	content, err := xhermes.GenerateHTML(HermesDefaultProduct(medicCrewLogoUrl), body)
	if err != nil {
		return err
	}

	err = xmail.SendMail(reference.ReferenceEmail, fmt.Sprintf("[%s] %s", xconfig.MailConf.Name, xi18n.LocalizeWithLangAndTemplateData(lang, &emailSubject, i18nMap)), "text/html", content)
	if err != nil {
		return err
	}

	return nil
}

type ProfessionalReferenceFormSaveReq struct {
	ProfessionalId          uint64                           `json:"professionalId" binding:"required"`
	FormUuid                string                           `json:"formUuid" binding:"required"`
	MatchUuid               string                           `json:"matchUuid" binding:"required"`
	Answers                 []ProfessionalReferenceAnswerReq `json:"answers" binding:"required,dive"`
	ConfirmationChecked     string                           `json:"confirmationChecked" binding:"omitempty,oneof=Y N"` // 表單確認部分，通常包含確認條款和勾選框
	ElectronicSignatureUuid string                           `json:"electronicSignatureUuid"`                           // 電子簽名設置，用於表單的電子簽名功能
	Submit                  string                           `json:"submit" binding:"required,oneof=Y N"`
}

type ProfessionalReferenceAnswerReq struct {
	Id     string `json:"id"`     // 字段的唯一標識符
	Value  string `json:"value"`  // 字段的預設值或用戶輸入值
	Remark string `json:"remark"` // 字段的備註，用於添加額外信息,例如選No時候需要填寫的內容
}
type ProfessionalReferenceFormSaveResp struct {
	Status string `json:"status"` // SUCCESS NOT_FOUND MISS_REQUIREMENT
}

// Save 保存專業參考表單
func (s *professionalReferenceFormService) Save(db *gorm.DB, req ProfessionalReferenceFormSaveReq) (ProfessionalReferenceFormSaveResp, error) {
	var resp ProfessionalReferenceFormSaveResp
	var professional model.Professional
	if err := db.Where("status = ?", model.ProfessionalStatusReviewing).Where("id = ?", req.ProfessionalId).First(&professional).Error; xgorm.IsSqlErr(err) {
		return resp, err
	}
	if professional.Id == 0 {
		resp.Status = "NOT_FOUND"
	} else {
		profile, err := professional.UnmarshalProfile(professional.ProfileJson)
		if err != nil {
			return resp, err
		}

		for i, r := range profile.References {
			if r.FormUuid == req.FormUuid && r.FormStatus == model.ProfessionalProfileReferencesFormStatusPending {
				var form model.ProfessionalReferenceForm
				err = json.Unmarshal([]byte(r.FormData), &form)
				if err != nil {
					return resp, err
				}

				var foundMatchUuid bool
				for _, matchUuid := range form.MatchUuids {
					if matchUuid == req.MatchUuid {
						foundMatchUuid = true
						break
					}
				}
				if !foundMatchUuid {
					resp.Status = "NOT_FOUND"
					return resp, nil
				}

				// 插入 Value，更新 Status
				idForAnswer := make(map[string]ProfessionalReferenceAnswerReq)
				for _, answer := range req.Answers {
					idForAnswer[answer.Id] = answer
				}

				if req.Submit == "Y" {
					profile.References[i].FormStatus = model.ProfessionalProfileReferencesFormStatusApproved
				}

				for x, section := range form.Sections {
					for y, field := range section.Fields {
						fieldId := form.Sections[x].Fields[y].Id
						form.Sections[x].Fields[y].Value = idForAnswer[fieldId].Value
						form.Sections[x].Fields[y].Remark = idForAnswer[fieldId].Remark
						if req.Submit == "Y" {
							if field.Required == "Y" && idForAnswer[fieldId].Value == "" {
								resp.Status = "MISS_REQUIREMENT"
								return resp, nil
							}
							for _, option := range field.Options {
								if option.Value == idForAnswer[fieldId].Value && option.RemarkRequired == "Y" && idForAnswer[fieldId].Remark == "" {
									// 某選項選中後，要求Remark必填
									resp.Status = "MISS_REQUIREMENT"
									return resp, nil
								}
							}
							if field.ExpectedValue != "" && field.ExpectedValue != idForAnswer[fieldId].Value {
								profile.References[i].FormStatus = model.ProfessionalProfileReferencesFormStatusNeedApproved
							}
						}
					}
				}
				form.ElectronicSignature.ProfessionalFileUuid = req.ElectronicSignatureUuid
				form.Confirmation.Checked = req.ConfirmationChecked
				if req.Submit == "Y" && (form.ElectronicSignature.ProfessionalFileUuid == "" || form.Confirmation.Checked == "") {
					resp.Status = "MISS_REQUIREMENT"
					return resp, nil
				}
				var formJson []byte
				formJson, err = json.Marshal(form)
				if err != nil {
					return resp, err
				}
				profile.References[i].FormData = string(formJson)
			}
		}

		if req.Submit == "Y" {
			professional.ReferenceFormStatus = model.ProfessionalReferenceFormStatusFilled
			for _, reference := range profile.References {
				if reference.FormStatus == model.ProfessionalProfileReferencesFormStatusPending || reference.FormStatus == "" {
					professional.ReferenceFormStatus = model.ProfessionalReferenceFormStatusUnfilled
					break
				}
			}
		}

		err = professional.MarshalProfile(profile)
		if err != nil {
			return resp, err
		}
		if err = db.Save(&professional).Error; err != nil {
			return resp, err
		}
		resp.Status = "SUCCESS"
		return resp, nil
	}
	resp.Status = "SUCCESS"
	return resp, nil
}

type ProfessionalReferenceFormGenUploadQrCodeReq struct {
	ProfessionalId uint64 `json:"professionalId" binding:"required"`
	FormUuid       string `json:"formUuid" binding:"required"`
	MatchUuid      string `json:"matchUuid" binding:"required"`
}

type ProfessionalReferenceFormGenUploadQrCodeResp struct {
	Valid      string    `json:"valid"` // Y N
	Uuid       string    `json:"uuid"`
	ExpireTime time.Time `json:"expireTime"`
}

type ProfessionalReferenceFormFileGetPreviewReq struct {
	ProfessionalId uint64 `form:"professionalId" binding:"required"`
	FormUuid       string `form:"formUuid" binding:"required"`
	MatchUuid      string `form:"matchUuid" binding:"required"`
	Uuid           string `form:"uuid" binding:"required"` // 二維碼的 uuid
}

type ProfessionalReferenceFormFileGetPreviewResp struct {
	Valid    string `json:"valid"`    // Y N
	FileUuid string `json:"fileUuid"` // 文件的uuid
	FileData string `json:"fileData"` // base64
}
